"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/embla-carousel-fade";
exports.ids = ["vendor-chunks/embla-carousel-fade"];
exports.modules = {

/***/ "(ssr)/../../node_modules/embla-carousel-fade/esm/embla-carousel-fade.esm.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/embla-carousel-fade/esm/embla-carousel-fade.esm.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Fade)\n/* harmony export */ });\nfunction clampNumber(number, min, max) {\n    return Math.min(Math.max(number, min), max);\n}\nfunction isNumber(value) {\n    return typeof value === \"number\" && !isNaN(value);\n}\nfunction Fade(userOptions = {}) {\n    const fullOpacity = 1;\n    const noOpacity = 0;\n    const fadeFriction = 0.68;\n    let emblaApi;\n    let opacities = [];\n    let fadeToNextDistance;\n    let distanceFromPointerDown = 0;\n    let fadeVelocity = 0;\n    let progress = 0;\n    let shouldFadePair = false;\n    let defaultSettledBehaviour;\n    let defaultProgressBehaviour;\n    function init(emblaApiInstance) {\n        emblaApi = emblaApiInstance;\n        const selectedSnap = emblaApi.selectedScrollSnap();\n        const { scrollBody, containerRect, axis } = emblaApi.internalEngine();\n        const containerSize = axis.measureSize(containerRect);\n        fadeToNextDistance = clampNumber(containerSize * 0.75, 200, 500);\n        shouldFadePair = false;\n        opacities = emblaApi.scrollSnapList().map((_, index)=>index === selectedSnap ? fullOpacity : noOpacity);\n        defaultSettledBehaviour = scrollBody.settled;\n        defaultProgressBehaviour = emblaApi.scrollProgress;\n        scrollBody.settled = settled;\n        emblaApi.scrollProgress = scrollProgress;\n        emblaApi.on(\"select\", select).on(\"slideFocus\", fadeToSelectedSnapInstantly).on(\"pointerDown\", pointerDown).on(\"pointerUp\", pointerUp);\n        disableScroll();\n        fadeToSelectedSnapInstantly();\n    }\n    function destroy() {\n        const { scrollBody } = emblaApi.internalEngine();\n        scrollBody.settled = defaultSettledBehaviour;\n        emblaApi.scrollProgress = defaultProgressBehaviour;\n        emblaApi.off(\"select\", select).off(\"slideFocus\", fadeToSelectedSnapInstantly).off(\"pointerDown\", pointerDown).off(\"pointerUp\", pointerUp);\n        emblaApi.slideNodes().forEach((slideNode)=>{\n            const slideStyle = slideNode.style;\n            slideStyle.opacity = \"\";\n            slideStyle.transform = \"\";\n            slideStyle.pointerEvents = \"\";\n            if (!slideNode.getAttribute(\"style\")) slideNode.removeAttribute(\"style\");\n        });\n    }\n    function fadeToSelectedSnapInstantly() {\n        const selectedSnap = emblaApi.selectedScrollSnap();\n        setOpacities(selectedSnap, fullOpacity);\n    }\n    function pointerUp() {\n        shouldFadePair = false;\n    }\n    function pointerDown() {\n        shouldFadePair = false;\n        distanceFromPointerDown = 0;\n        fadeVelocity = 0;\n    }\n    function select() {\n        const duration = emblaApi.internalEngine().scrollBody.duration();\n        fadeVelocity = duration ? 0 : fullOpacity;\n        shouldFadePair = true;\n        if (!duration) fadeToSelectedSnapInstantly();\n    }\n    function getSlideTransform(position) {\n        const { axis } = emblaApi.internalEngine();\n        const translateAxis = axis.scroll.toUpperCase();\n        return `translate${translateAxis}(${axis.direction(position)}px)`;\n    }\n    function disableScroll() {\n        const { translate, slideLooper } = emblaApi.internalEngine();\n        translate.clear();\n        translate.toggleActive(false);\n        slideLooper.loopPoints.forEach(({ translate })=>{\n            translate.clear();\n            translate.toggleActive(false);\n        });\n    }\n    function lockExcessiveScroll(fadeIndex) {\n        const { scrollSnaps, location, target } = emblaApi.internalEngine();\n        if (!isNumber(fadeIndex) || opacities[fadeIndex] < 0.5) return;\n        location.set(scrollSnaps[fadeIndex]);\n        target.set(location);\n    }\n    function setOpacities(fadeIndex, velocity) {\n        const scrollSnaps = emblaApi.scrollSnapList();\n        scrollSnaps.forEach((_, indexA)=>{\n            const absVelocity = Math.abs(velocity);\n            const currentOpacity = opacities[indexA];\n            const isFadeIndex = indexA === fadeIndex;\n            const nextOpacity = isFadeIndex ? currentOpacity + absVelocity : currentOpacity - absVelocity;\n            const clampedOpacity = clampNumber(nextOpacity, noOpacity, fullOpacity);\n            opacities[indexA] = clampedOpacity;\n            const fadePair = isFadeIndex && shouldFadePair;\n            const indexB = emblaApi.previousScrollSnap();\n            if (fadePair) opacities[indexB] = 1 - clampedOpacity;\n            if (isFadeIndex) setProgress(fadeIndex, clampedOpacity);\n            setOpacity(indexA);\n        });\n    }\n    function setOpacity(index) {\n        const slidesInSnap = emblaApi.internalEngine().slideRegistry[index];\n        const { scrollSnaps, containerRect } = emblaApi.internalEngine();\n        const opacity = opacities[index];\n        slidesInSnap.forEach((slideIndex)=>{\n            const slideStyle = emblaApi.slideNodes()[slideIndex].style;\n            const roundedOpacity = parseFloat(opacity.toFixed(2));\n            const hasOpacity = roundedOpacity > noOpacity;\n            const position = hasOpacity ? scrollSnaps[index] : containerRect.width + 2;\n            const transform = getSlideTransform(position);\n            if (hasOpacity) slideStyle.transform = transform;\n            slideStyle.opacity = roundedOpacity.toString();\n            slideStyle.pointerEvents = opacity > 0.5 ? \"auto\" : \"none\";\n            if (!hasOpacity) slideStyle.transform = transform;\n        });\n    }\n    function setProgress(fadeIndex, opacity) {\n        const { index, dragHandler, scrollSnaps } = emblaApi.internalEngine();\n        const pointerDown = dragHandler.pointerDown();\n        const snapFraction = 1 / (scrollSnaps.length - 1);\n        let indexA = fadeIndex;\n        let indexB = pointerDown ? emblaApi.selectedScrollSnap() : emblaApi.previousScrollSnap();\n        if (pointerDown && indexA === indexB) {\n            const reverseSign = Math.sign(distanceFromPointerDown) * -1;\n            indexA = indexB;\n            indexB = index.clone().set(indexB).add(reverseSign).get();\n        }\n        const currentPosition = indexB * snapFraction;\n        const diffPosition = (indexA - indexB) * snapFraction;\n        progress = currentPosition + diffPosition * opacity;\n    }\n    function getFadeIndex() {\n        const { dragHandler, index, scrollBody } = emblaApi.internalEngine();\n        const selectedSnap = emblaApi.selectedScrollSnap();\n        if (!dragHandler.pointerDown()) return selectedSnap;\n        const directionSign = Math.sign(scrollBody.velocity());\n        const distanceSign = Math.sign(distanceFromPointerDown);\n        const nextSnap = index.clone().set(selectedSnap).add(directionSign * -1).get();\n        if (!directionSign || !distanceSign) return null;\n        return distanceSign === directionSign ? nextSnap : selectedSnap;\n    }\n    const fade = (emblaApi)=>{\n        const { dragHandler, scrollBody } = emblaApi.internalEngine();\n        const pointerDown = dragHandler.pointerDown();\n        const velocity = scrollBody.velocity();\n        const duration = scrollBody.duration();\n        const fadeIndex = getFadeIndex();\n        const noFadeIndex = !isNumber(fadeIndex);\n        if (pointerDown) {\n            if (!velocity) return;\n            distanceFromPointerDown += velocity;\n            fadeVelocity = Math.abs(velocity / fadeToNextDistance);\n            lockExcessiveScroll(fadeIndex);\n        }\n        if (!pointerDown) {\n            if (!duration || noFadeIndex) return;\n            fadeVelocity += (fullOpacity - opacities[fadeIndex]) / duration;\n            fadeVelocity *= fadeFriction;\n        }\n        if (noFadeIndex) return;\n        setOpacities(fadeIndex, fadeVelocity);\n    };\n    function settled() {\n        const { target, location } = emblaApi.internalEngine();\n        const diffToTarget = target.get() - location.get();\n        const notReachedTarget = Math.abs(diffToTarget) >= 1;\n        const fadeIndex = getFadeIndex();\n        const noFadeIndex = !isNumber(fadeIndex);\n        fade(emblaApi);\n        if (noFadeIndex || notReachedTarget) return false;\n        return opacities[fadeIndex] > 0.999;\n    }\n    function scrollProgress() {\n        return progress;\n    }\n    const self = {\n        name: \"fade\",\n        options: userOptions,\n        init,\n        destroy\n    };\n    return self;\n}\nFade.globalOptions = undefined;\n //# sourceMappingURL=embla-carousel-fade.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/embla-carousel-fade/esm/embla-carousel-fade.esm.js\n");

/***/ })

};
;