"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@restart";
exports.ids = ["vendor-chunks/@restart"];
exports.modules = {

/***/ "(ssr)/../../node_modules/@restart/hooks/esm/useBreakpoint.js":
/*!**************************************************************!*\
  !*** ../../node_modules/@restart/hooks/esm/useBreakpoint.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBreakpointHook: () => (/* binding */ createBreakpointHook),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _useMediaQuery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useMediaQuery */ \"(ssr)/../../node_modules/@restart/hooks/esm/useMediaQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/**\n * Create a responsive hook we a set of breakpoint names and widths.\n * You can use any valid css units as well as a numbers (for pixels).\n *\n * **NOTE:** The object key order is important! it's assumed to be in order from smallest to largest\n *\n * ```ts\n * const useBreakpoint = createBreakpointHook({\n *  xs: 0,\n *  sm: 576,\n *  md: 768,\n *  lg: 992,\n *  xl: 1200,\n * })\n * ```\n *\n * **Watch out!** using string values will sometimes construct media queries using css `calc()` which\n * is NOT supported in media queries by all browsers at the moment. use numbers for\n * the widest range of browser support.\n *\n * @param breakpointValues A object hash of names to breakpoint dimensions\n */ function createBreakpointHook(breakpointValues) {\n    const names = Object.keys(breakpointValues);\n    function and(query, next) {\n        if (query === next) {\n            return next;\n        }\n        return query ? `${query} and ${next}` : next;\n    }\n    function getNext(breakpoint) {\n        return names[Math.min(names.indexOf(breakpoint) + 1, names.length - 1)];\n    }\n    function getMaxQuery(breakpoint) {\n        const next = getNext(breakpoint);\n        let value = breakpointValues[next];\n        if (typeof value === \"number\") value = `${value - 0.2}px`;\n        else value = `calc(${value} - 0.2px)`;\n        return `(max-width: ${value})`;\n    }\n    function getMinQuery(breakpoint) {\n        let value = breakpointValues[breakpoint];\n        if (typeof value === \"number\") {\n            value = `${value}px`;\n        }\n        return `(min-width: ${value})`;\n    }\n    /**\n   * Match a set of breakpoints\n   *\n   * ```tsx\n   * const MidSizeOnly = () => {\n   *   const isMid = useBreakpoint({ lg: 'down', sm: 'up' });\n   *\n   *   if (isMid) return <div>On a Reasonable sized Screen!</div>\n   *   return null;\n   * }\n   * ```\n   * @param breakpointMap An object map of breakpoints and directions, queries are constructed using \"and\" to join\n   * breakpoints together\n   * @param window Optionally specify the target window to match against (useful when rendering into iframes)\n   */ /**\n   * Match a single breakpoint exactly, up, or down.\n   *\n   * ```tsx\n   * const PhoneOnly = () => {\n   *   const isSmall = useBreakpoint('sm', 'down');\n   *\n   *   if (isSmall) return <div>On a Small Screen!</div>\n   *   return null;\n   * }\n   * ```\n   *\n   * @param breakpoint The breakpoint key\n   * @param direction A direction 'up' for a max, 'down' for min, true to match only the breakpoint\n   * @param window Optionally specify the target window to match against (useful when rendering into iframes)\n   */ function useBreakpoint(breakpointOrMap, direction, window) {\n        let breakpointMap;\n        if (typeof breakpointOrMap === \"object\") {\n            breakpointMap = breakpointOrMap;\n            window = direction;\n            direction = true;\n        } else {\n            direction = direction || true;\n            breakpointMap = {\n                [breakpointOrMap]: direction\n            };\n        }\n        let query = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>Object.entries(breakpointMap).reduce((query, [key, direction])=>{\n                if (direction === \"up\" || direction === true) {\n                    query = and(query, getMinQuery(key));\n                }\n                if (direction === \"down\" || direction === true) {\n                    query = and(query, getMaxQuery(key));\n                }\n                return query;\n            }, \"\"), [\n            JSON.stringify(breakpointMap)\n        ]);\n        return (0,_useMediaQuery__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(query, window);\n    }\n    return useBreakpoint;\n}\nconst useBreakpoint = createBreakpointHook({\n    xs: 0,\n    sm: 576,\n    md: 768,\n    lg: 992,\n    xl: 1200,\n    xxl: 1400\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useBreakpoint);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/hooks/esm/useBreakpoint.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/hooks/esm/useCallbackRef.js":
/*!***************************************************************!*\
  !*** ../../node_modules/@restart/hooks/esm/useCallbackRef.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * A convenience hook around `useState` designed to be paired with\n * the component [callback ref](https://reactjs.org/docs/refs-and-the-dom.html#callback-refs) api.\n * Callback refs are useful over `useRef()` when you need to respond to the ref being set\n * instead of lazily accessing it in an effect.\n *\n * ```ts\n * const [element, attachRef] = useCallbackRef<HTMLDivElement>()\n *\n * useEffect(() => {\n *   if (!element) return\n *\n *   const calendar = new FullCalendar.Calendar(element)\n *\n *   return () => {\n *     calendar.destroy()\n *   }\n * }, [element])\n *\n * return <div ref={attachRef} />\n * ```\n *\n * @category refs\n */ function useCallbackRef() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L2hvb2tzL2VzbS91c2VDYWxsYmFja1JlZi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUM7QUFFakM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBdUJDLEdBQ2MsU0FBU0M7SUFDdEIsT0FBT0QsK0NBQVFBLENBQUM7QUFDbEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvZXNtL3VzZUNhbGxiYWNrUmVmLmpzPzNiYjMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5cbi8qKlxuICogQSBjb252ZW5pZW5jZSBob29rIGFyb3VuZCBgdXNlU3RhdGVgIGRlc2lnbmVkIHRvIGJlIHBhaXJlZCB3aXRoXG4gKiB0aGUgY29tcG9uZW50IFtjYWxsYmFjayByZWZdKGh0dHBzOi8vcmVhY3Rqcy5vcmcvZG9jcy9yZWZzLWFuZC10aGUtZG9tLmh0bWwjY2FsbGJhY2stcmVmcykgYXBpLlxuICogQ2FsbGJhY2sgcmVmcyBhcmUgdXNlZnVsIG92ZXIgYHVzZVJlZigpYCB3aGVuIHlvdSBuZWVkIHRvIHJlc3BvbmQgdG8gdGhlIHJlZiBiZWluZyBzZXRcbiAqIGluc3RlYWQgb2YgbGF6aWx5IGFjY2Vzc2luZyBpdCBpbiBhbiBlZmZlY3QuXG4gKlxuICogYGBgdHNcbiAqIGNvbnN0IFtlbGVtZW50LCBhdHRhY2hSZWZdID0gdXNlQ2FsbGJhY2tSZWY8SFRNTERpdkVsZW1lbnQ+KClcbiAqXG4gKiB1c2VFZmZlY3QoKCkgPT4ge1xuICogICBpZiAoIWVsZW1lbnQpIHJldHVyblxuICpcbiAqICAgY29uc3QgY2FsZW5kYXIgPSBuZXcgRnVsbENhbGVuZGFyLkNhbGVuZGFyKGVsZW1lbnQpXG4gKlxuICogICByZXR1cm4gKCkgPT4ge1xuICogICAgIGNhbGVuZGFyLmRlc3Ryb3koKVxuICogICB9XG4gKiB9LCBbZWxlbWVudF0pXG4gKlxuICogcmV0dXJuIDxkaXYgcmVmPXthdHRhY2hSZWZ9IC8+XG4gKiBgYGBcbiAqXG4gKiBAY2F0ZWdvcnkgcmVmc1xuICovXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1c2VDYWxsYmFja1JlZigpIHtcbiAgcmV0dXJuIHVzZVN0YXRlKG51bGwpO1xufSJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUNhbGxiYWNrUmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/hooks/esm/useCallbackRef.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/hooks/esm/useCommittedRef.js":
/*!****************************************************************!*\
  !*** ../../node_modules/@restart/hooks/esm/useCommittedRef.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * Creates a `Ref` whose value is updated in an effect, ensuring the most recent\n * value is the one rendered with. Generally only required for Concurrent mode usage\n * where previous work in `render()` may be discarded before being used.\n *\n * This is safe to access in an event handler.\n *\n * @param value The `Ref` value\n */ function useCommittedRef(value) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        ref.current = value;\n    }, [\n        value\n    ]);\n    return ref;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useCommittedRef);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L2hvb2tzL2VzbS91c2VDb21taXR0ZWRSZWYuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTBDO0FBRTFDOzs7Ozs7OztDQVFDLEdBQ0QsU0FBU0UsZ0JBQWdCQyxLQUFLO0lBQzVCLE1BQU1DLE1BQU1ILDZDQUFNQSxDQUFDRTtJQUNuQkgsZ0RBQVNBLENBQUM7UUFDUkksSUFBSUMsT0FBTyxHQUFHRjtJQUNoQixHQUFHO1FBQUNBO0tBQU07SUFDVixPQUFPQztBQUNUO0FBQ0EsaUVBQWVGLGVBQWVBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvZXNtL3VzZUNvbW1pdHRlZFJlZi5qcz8yMjc2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuXG4vKipcbiAqIENyZWF0ZXMgYSBgUmVmYCB3aG9zZSB2YWx1ZSBpcyB1cGRhdGVkIGluIGFuIGVmZmVjdCwgZW5zdXJpbmcgdGhlIG1vc3QgcmVjZW50XG4gKiB2YWx1ZSBpcyB0aGUgb25lIHJlbmRlcmVkIHdpdGguIEdlbmVyYWxseSBvbmx5IHJlcXVpcmVkIGZvciBDb25jdXJyZW50IG1vZGUgdXNhZ2VcbiAqIHdoZXJlIHByZXZpb3VzIHdvcmsgaW4gYHJlbmRlcigpYCBtYXkgYmUgZGlzY2FyZGVkIGJlZm9yZSBiZWluZyB1c2VkLlxuICpcbiAqIFRoaXMgaXMgc2FmZSB0byBhY2Nlc3MgaW4gYW4gZXZlbnQgaGFuZGxlci5cbiAqXG4gKiBAcGFyYW0gdmFsdWUgVGhlIGBSZWZgIHZhbHVlXG4gKi9cbmZ1bmN0aW9uIHVzZUNvbW1pdHRlZFJlZih2YWx1ZSkge1xuICBjb25zdCByZWYgPSB1c2VSZWYodmFsdWUpO1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHJlZi5jdXJyZW50ID0gdmFsdWU7XG4gIH0sIFt2YWx1ZV0pO1xuICByZXR1cm4gcmVmO1xufVxuZXhwb3J0IGRlZmF1bHQgdXNlQ29tbWl0dGVkUmVmOyJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJ1c2VDb21taXR0ZWRSZWYiLCJ2YWx1ZSIsInJlZiIsImN1cnJlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/hooks/esm/useCommittedRef.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/hooks/esm/useEventCallback.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/@restart/hooks/esm/useEventCallback.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useEventCallback)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useCommittedRef__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useCommittedRef */ \"(ssr)/../../node_modules/@restart/hooks/esm/useCommittedRef.js\");\n\n\nfunction useEventCallback(fn) {\n    const ref = (0,_useCommittedRef__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(fn);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(...args) {\n        return ref.current && ref.current(...args);\n    }, [\n        ref\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L2hvb2tzL2VzbS91c2VFdmVudENhbGxiYWNrLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBb0M7QUFDWTtBQUNqQyxTQUFTRSxpQkFBaUJDLEVBQUU7SUFDekMsTUFBTUMsTUFBTUgsNERBQWVBLENBQUNFO0lBQzVCLE9BQU9ILGtEQUFXQSxDQUFDLFNBQVUsR0FBR0ssSUFBSTtRQUNsQyxPQUFPRCxJQUFJRSxPQUFPLElBQUlGLElBQUlFLE9BQU8sSUFBSUQ7SUFDdkMsR0FBRztRQUFDRDtLQUFJO0FBQ1YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvZXNtL3VzZUV2ZW50Q2FsbGJhY2suanM/MzBlNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB1c2VDb21taXR0ZWRSZWYgZnJvbSAnLi91c2VDb21taXR0ZWRSZWYnO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlRXZlbnRDYWxsYmFjayhmbikge1xuICBjb25zdCByZWYgPSB1c2VDb21taXR0ZWRSZWYoZm4pO1xuICByZXR1cm4gdXNlQ2FsbGJhY2soZnVuY3Rpb24gKC4uLmFyZ3MpIHtcbiAgICByZXR1cm4gcmVmLmN1cnJlbnQgJiYgcmVmLmN1cnJlbnQoLi4uYXJncyk7XG4gIH0sIFtyZWZdKTtcbn0iXSwibmFtZXMiOlsidXNlQ2FsbGJhY2siLCJ1c2VDb21taXR0ZWRSZWYiLCJ1c2VFdmVudENhbGxiYWNrIiwiZm4iLCJyZWYiLCJhcmdzIiwiY3VycmVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/hooks/esm/useEventCallback.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/hooks/esm/useIsomorphicEffect.js":
/*!********************************************************************!*\
  !*** ../../node_modules/@restart/hooks/esm/useIsomorphicEffect.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst isReactNative = typeof global !== \"undefined\" && // @ts-ignore\nglobal.navigator && // @ts-ignore\nglobal.navigator.product === \"ReactNative\";\nconst isDOM = typeof document !== \"undefined\";\n/**\n * Is `useLayoutEffect` in a DOM or React Native environment, otherwise resolves to useEffect\n * Only useful to avoid the console warning.\n *\n * PREFER `useEffect` UNLESS YOU KNOW WHAT YOU ARE DOING.\n *\n * @category effects\n */ /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isDOM || isReactNative ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L2hvb2tzL2VzbS91c2VJc29tb3JwaGljRWZmZWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFtRDtBQUNuRCxNQUFNRSxnQkFBZ0IsT0FBT0MsV0FBVyxlQUN4QyxhQUFhO0FBQ2JBLE9BQU9DLFNBQVMsSUFDaEIsYUFBYTtBQUNiRCxPQUFPQyxTQUFTLENBQUNDLE9BQU8sS0FBSztBQUM3QixNQUFNQyxRQUFRLE9BQU9DLGFBQWE7QUFFbEM7Ozs7Ozs7Q0FPQyxHQUNELGlFQUFlRCxTQUFTSixnQkFBZ0JELGtEQUFlQSxHQUFHRCw0Q0FBU0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9AcmVzdGFydC9ob29rcy9lc20vdXNlSXNvbW9ycGhpY0VmZmVjdC5qcz84OTM3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCwgdXNlTGF5b3V0RWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuY29uc3QgaXNSZWFjdE5hdGl2ZSA9IHR5cGVvZiBnbG9iYWwgIT09ICd1bmRlZmluZWQnICYmXG4vLyBAdHMtaWdub3JlXG5nbG9iYWwubmF2aWdhdG9yICYmXG4vLyBAdHMtaWdub3JlXG5nbG9iYWwubmF2aWdhdG9yLnByb2R1Y3QgPT09ICdSZWFjdE5hdGl2ZSc7XG5jb25zdCBpc0RPTSA9IHR5cGVvZiBkb2N1bWVudCAhPT0gJ3VuZGVmaW5lZCc7XG5cbi8qKlxuICogSXMgYHVzZUxheW91dEVmZmVjdGAgaW4gYSBET00gb3IgUmVhY3QgTmF0aXZlIGVudmlyb25tZW50LCBvdGhlcndpc2UgcmVzb2x2ZXMgdG8gdXNlRWZmZWN0XG4gKiBPbmx5IHVzZWZ1bCB0byBhdm9pZCB0aGUgY29uc29sZSB3YXJuaW5nLlxuICpcbiAqIFBSRUZFUiBgdXNlRWZmZWN0YCBVTkxFU1MgWU9VIEtOT1cgV0hBVCBZT1UgQVJFIERPSU5HLlxuICpcbiAqIEBjYXRlZ29yeSBlZmZlY3RzXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGlzRE9NIHx8IGlzUmVhY3ROYXRpdmUgPyB1c2VMYXlvdXRFZmZlY3QgOiB1c2VFZmZlY3Q7Il0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZUxheW91dEVmZmVjdCIsImlzUmVhY3ROYXRpdmUiLCJnbG9iYWwiLCJuYXZpZ2F0b3IiLCJwcm9kdWN0IiwiaXNET00iLCJkb2N1bWVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/hooks/esm/useIsomorphicEffect.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/hooks/esm/useMediaQuery.js":
/*!**************************************************************!*\
  !*** ../../node_modules/@restart/hooks/esm/useMediaQuery.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useMediaQuery)\n/* harmony export */ });\n/* harmony import */ var _useIsomorphicEffect__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useIsomorphicEffect */ \"(ssr)/../../node_modules/@restart/hooks/esm/useIsomorphicEffect.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst matchersByWindow = new WeakMap();\nconst getMatcher = (query, targetWindow)=>{\n    if (!query || !targetWindow) return undefined;\n    const matchers = matchersByWindow.get(targetWindow) || new Map();\n    matchersByWindow.set(targetWindow, matchers);\n    let mql = matchers.get(query);\n    if (!mql) {\n        mql = targetWindow.matchMedia(query);\n        mql.refCount = 0;\n        matchers.set(mql.media, mql);\n    }\n    return mql;\n};\n/**\n * Match a media query and get updates as the match changes. The media string is\n * passed directly to `window.matchMedia` and run as a Layout Effect, so initial\n * matches are returned before the browser has a chance to paint.\n *\n * ```tsx\n * function Page() {\n *   const isWide = useMediaQuery('min-width: 1000px')\n *\n *   return isWide ? \"very wide\" : 'not so wide'\n * }\n * ```\n *\n * Media query lists are also reused globally, hook calls for the same query\n * will only create a matcher once under the hood.\n *\n * @param query A media query\n * @param targetWindow The window to match against, uses the globally available one as a default.\n */ function useMediaQuery(query, targetWindow =  true ? undefined : 0) {\n    const mql = getMatcher(query, targetWindow);\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>mql ? mql.matches : false);\n    (0,_useIsomorphicEffect__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(()=>{\n        let mql = getMatcher(query, targetWindow);\n        if (!mql) {\n            return setMatches(false);\n        }\n        let matchers = matchersByWindow.get(targetWindow);\n        const handleChange = ()=>{\n            setMatches(mql.matches);\n        };\n        mql.refCount++;\n        mql.addListener(handleChange);\n        handleChange();\n        return ()=>{\n            mql.removeListener(handleChange);\n            mql.refCount--;\n            if (mql.refCount <= 0) {\n                matchers == null ? void 0 : matchers.delete(mql.media);\n            }\n            mql = undefined;\n        };\n    }, [\n        query\n    ]);\n    return matches;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/hooks/esm/useMediaQuery.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/hooks/esm/useMergedRefs.js":
/*!**************************************************************!*\
  !*** ../../node_modules/@restart/hooks/esm/useMergedRefs.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   mergeRefs: () => (/* binding */ mergeRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst toFnRef = (ref)=>!ref || typeof ref === \"function\" ? ref : (value)=>{\n        ref.current = value;\n    };\nfunction mergeRefs(refA, refB) {\n    const a = toFnRef(refA);\n    const b = toFnRef(refB);\n    return (value)=>{\n        if (a) a(value);\n        if (b) b(value);\n    };\n}\n/**\n * Create and returns a single callback ref composed from two other Refs.\n *\n * ```tsx\n * const Button = React.forwardRef((props, ref) => {\n *   const [element, attachRef] = useCallbackRef<HTMLButtonElement>();\n *   const mergedRef = useMergedRefs(ref, attachRef);\n *\n *   return <button ref={mergedRef} {...props}/>\n * })\n * ```\n *\n * @param refA A Callback or mutable Ref\n * @param refB A Callback or mutable Ref\n * @category refs\n */ function useMergedRefs(refA, refB) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>mergeRefs(refA, refB), [\n        refA,\n        refB\n    ]);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useMergedRefs);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/hooks/esm/useMergedRefs.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/hooks/esm/useUpdatedRef.js":
/*!**************************************************************!*\
  !*** ../../node_modules/@restart/hooks/esm/useUpdatedRef.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useUpdatedRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * Returns a ref that is immediately updated with the new value\n *\n * @param value The Ref value\n * @category refs\n */ function useUpdatedRef(value) {\n    const valueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);\n    valueRef.current = value;\n    return valueRef;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L2hvb2tzL2VzbS91c2VVcGRhdGVkUmVmLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErQjtBQUUvQjs7Ozs7Q0FLQyxHQUNjLFNBQVNDLGNBQWNDLEtBQUs7SUFDekMsTUFBTUMsV0FBV0gsNkNBQU1BLENBQUNFO0lBQ3hCQyxTQUFTQyxPQUFPLEdBQUdGO0lBQ25CLE9BQU9DO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvZXNtL3VzZVVwZGF0ZWRSZWYuanM/MDk2MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5cbi8qKlxuICogUmV0dXJucyBhIHJlZiB0aGF0IGlzIGltbWVkaWF0ZWx5IHVwZGF0ZWQgd2l0aCB0aGUgbmV3IHZhbHVlXG4gKlxuICogQHBhcmFtIHZhbHVlIFRoZSBSZWYgdmFsdWVcbiAqIEBjYXRlZ29yeSByZWZzXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZVVwZGF0ZWRSZWYodmFsdWUpIHtcbiAgY29uc3QgdmFsdWVSZWYgPSB1c2VSZWYodmFsdWUpO1xuICB2YWx1ZVJlZi5jdXJyZW50ID0gdmFsdWU7XG4gIHJldHVybiB2YWx1ZVJlZjtcbn0iXSwibmFtZXMiOlsidXNlUmVmIiwidXNlVXBkYXRlZFJlZiIsInZhbHVlIiwidmFsdWVSZWYiLCJjdXJyZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/hooks/esm/useUpdatedRef.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/hooks/esm/useWillUnmount.js":
/*!***************************************************************!*\
  !*** ../../node_modules/@restart/hooks/esm/useWillUnmount.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useWillUnmount)\n/* harmony export */ });\n/* harmony import */ var _useUpdatedRef__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useUpdatedRef */ \"(ssr)/../../node_modules/@restart/hooks/esm/useUpdatedRef.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/**\n * Attach a callback that fires when a component unmounts\n *\n * @param fn Handler to run when the component unmounts\n * @category effects\n */ function useWillUnmount(fn) {\n    const onUnmount = (0,_useUpdatedRef__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(fn);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>()=>onUnmount.current(), []);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L2hvb2tzL2VzbS91c2VXaWxsVW5tb3VudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTRDO0FBQ1Y7QUFFbEM7Ozs7O0NBS0MsR0FDYyxTQUFTRSxlQUFlQyxFQUFFO0lBQ3ZDLE1BQU1DLFlBQVlKLDBEQUFhQSxDQUFDRztJQUNoQ0YsZ0RBQVNBLENBQUMsSUFBTSxJQUFNRyxVQUFVQyxPQUFPLElBQUksRUFBRTtBQUMvQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9AcmVzdGFydC9ob29rcy9lc20vdXNlV2lsbFVubW91bnQuanM/MmMzOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdXNlVXBkYXRlZFJlZiBmcm9tICcuL3VzZVVwZGF0ZWRSZWYnO1xuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuXG4vKipcbiAqIEF0dGFjaCBhIGNhbGxiYWNrIHRoYXQgZmlyZXMgd2hlbiBhIGNvbXBvbmVudCB1bm1vdW50c1xuICpcbiAqIEBwYXJhbSBmbiBIYW5kbGVyIHRvIHJ1biB3aGVuIHRoZSBjb21wb25lbnQgdW5tb3VudHNcbiAqIEBjYXRlZ29yeSBlZmZlY3RzXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZVdpbGxVbm1vdW50KGZuKSB7XG4gIGNvbnN0IG9uVW5tb3VudCA9IHVzZVVwZGF0ZWRSZWYoZm4pO1xuICB1c2VFZmZlY3QoKCkgPT4gKCkgPT4gb25Vbm1vdW50LmN1cnJlbnQoKSwgW10pO1xufSJdLCJuYW1lcyI6WyJ1c2VVcGRhdGVkUmVmIiwidXNlRWZmZWN0IiwidXNlV2lsbFVubW91bnQiLCJmbiIsIm9uVW5tb3VudCIsImN1cnJlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/hooks/esm/useWillUnmount.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/Anchor.js":
/*!****************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/Anchor.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports.isTrivialHref = isTrivialHref;\nexports[\"default\"] = void 0;\nvar React = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar _hooks = __webpack_require__(/*! @restart/hooks */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/index.js\");\nvar _Button = __webpack_require__(/*! ./Button */ \"(ssr)/../../node_modules/@restart/ui/cjs/Button.js\");\nvar _jsxRuntime = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\nconst _excluded = [\n    \"onKeyDown\"\n];\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\nfunction isTrivialHref(href) {\n    return !href || href.trim() === \"#\";\n}\n/**\n * An generic `<a>` component that covers a few A11y cases, ensuring that\n * cases where the `href` is missing or trivial like \"#\" are treated like buttons.\n */ const Anchor = /*#__PURE__*/ React.forwardRef((_ref, ref)=>{\n    let { onKeyDown } = _ref, props = _objectWithoutPropertiesLoose(_ref, _excluded);\n    const [buttonProps] = (0, _Button.useButtonProps)(Object.assign({\n        tagName: \"a\"\n    }, props));\n    const handleKeyDown = (0, _hooks.useEventCallback)((e)=>{\n        buttonProps.onKeyDown(e);\n        onKeyDown == null ? void 0 : onKeyDown(e);\n    });\n    if (isTrivialHref(props.href) || props.role === \"button\") {\n        return /*#__PURE__*/ (0, _jsxRuntime.jsx)(\"a\", Object.assign({\n            ref: ref\n        }, props, buttonProps, {\n            onKeyDown: handleKeyDown\n        }));\n    }\n    return /*#__PURE__*/ (0, _jsxRuntime.jsx)(\"a\", Object.assign({\n        ref: ref\n    }, props, {\n        onKeyDown: onKeyDown\n    }));\n});\nAnchor.displayName = \"Anchor\";\nvar _default = Anchor;\nexports[\"default\"] = _default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L3VpL2Nqcy9BbmNob3IuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFFYkEsa0JBQWtCLEdBQUc7QUFDckJBLHFCQUFxQixHQUFHRTtBQUN4QkYsa0JBQWUsR0FBRyxLQUFLO0FBQ3ZCLElBQUlJLFFBQVFDLHdCQUF3QkMsbUJBQU9BLENBQUMsNEdBQU87QUFDbkQsSUFBSUMsU0FBU0QsbUJBQU9BLENBQUMscUdBQWdCO0FBQ3JDLElBQUlFLFVBQVVGLG1CQUFPQSxDQUFDLG9FQUFVO0FBQ2hDLElBQUlHLGNBQWNILG1CQUFPQSxDQUFDLG9JQUFtQjtBQUM3QyxNQUFNSSxZQUFZO0lBQUM7Q0FBWTtBQUMvQixTQUFTQyx5QkFBeUJDLFdBQVc7SUFBSSxJQUFJLE9BQU9DLFlBQVksWUFBWSxPQUFPO0lBQU0sSUFBSUMsb0JBQW9CLElBQUlEO0lBQVcsSUFBSUUsbUJBQW1CLElBQUlGO0lBQVcsT0FBTyxDQUFDRiwyQkFBMkIsU0FBVUMsV0FBVztRQUFJLE9BQU9BLGNBQWNHLG1CQUFtQkQ7SUFBbUIsR0FBR0Y7QUFBYztBQUN0VCxTQUFTUCx3QkFBd0JXLEdBQUcsRUFBRUosV0FBVztJQUFJLElBQUksQ0FBQ0EsZUFBZUksT0FBT0EsSUFBSWYsVUFBVSxFQUFFO1FBQUUsT0FBT2U7SUFBSztJQUFFLElBQUlBLFFBQVEsUUFBUSxPQUFPQSxRQUFRLFlBQVksT0FBT0EsUUFBUSxZQUFZO1FBQUUsT0FBTztZQUFFYixTQUFTYTtRQUFJO0lBQUc7SUFBRSxJQUFJQyxRQUFRTix5QkFBeUJDO0lBQWMsSUFBSUssU0FBU0EsTUFBTUMsR0FBRyxDQUFDRixNQUFNO1FBQUUsT0FBT0MsTUFBTUUsR0FBRyxDQUFDSDtJQUFNO0lBQUUsSUFBSUksU0FBUyxDQUFDO0lBQUcsSUFBSUMsd0JBQXdCQyxPQUFPQyxjQUFjLElBQUlELE9BQU9FLHdCQUF3QjtJQUFFLElBQUssSUFBSUMsT0FBT1QsSUFBSztRQUFFLElBQUlTLFFBQVEsYUFBYUgsT0FBT0ksU0FBUyxDQUFDQyxjQUFjLENBQUNDLElBQUksQ0FBQ1osS0FBS1MsTUFBTTtZQUFFLElBQUlJLE9BQU9SLHdCQUF3QkMsT0FBT0Usd0JBQXdCLENBQUNSLEtBQUtTLE9BQU87WUFBTSxJQUFJSSxRQUFTQSxDQUFBQSxLQUFLVixHQUFHLElBQUlVLEtBQUtDLEdBQUcsR0FBRztnQkFBRVIsT0FBT0MsY0FBYyxDQUFDSCxRQUFRSyxLQUFLSTtZQUFPLE9BQU87Z0JBQUVULE1BQU0sQ0FBQ0ssSUFBSSxHQUFHVCxHQUFHLENBQUNTLElBQUk7WUFBRTtRQUFFO0lBQUU7SUFBRUwsT0FBT2pCLE9BQU8sR0FBR2E7SUFBSyxJQUFJQyxPQUFPO1FBQUVBLE1BQU1hLEdBQUcsQ0FBQ2QsS0FBS0k7SUFBUztJQUFFLE9BQU9BO0FBQVE7QUFDbnlCLFNBQVNXLDhCQUE4QkMsTUFBTSxFQUFFQyxRQUFRO0lBQUksSUFBSUQsVUFBVSxNQUFNLE9BQU8sQ0FBQztJQUFHLElBQUlFLFNBQVMsQ0FBQztJQUFHLElBQUlDLGFBQWFiLE9BQU9jLElBQUksQ0FBQ0o7SUFBUyxJQUFJUCxLQUFLWTtJQUFHLElBQUtBLElBQUksR0FBR0EsSUFBSUYsV0FBV0csTUFBTSxFQUFFRCxJQUFLO1FBQUVaLE1BQU1VLFVBQVUsQ0FBQ0UsRUFBRTtRQUFFLElBQUlKLFNBQVNNLE9BQU8sQ0FBQ2QsUUFBUSxHQUFHO1FBQVVTLE1BQU0sQ0FBQ1QsSUFBSSxHQUFHTyxNQUFNLENBQUNQLElBQUk7SUFBRTtJQUFFLE9BQU9TO0FBQVE7QUFDbFQsU0FBU2hDLGNBQWNzQyxJQUFJO0lBQ3pCLE9BQU8sQ0FBQ0EsUUFBUUEsS0FBS0MsSUFBSSxPQUFPO0FBQ2xDO0FBQ0E7OztDQUdDLEdBQ0QsTUFBTUMsU0FBUyxXQUFXLEdBQUV0QyxNQUFNdUMsVUFBVSxDQUFDLENBQUNDLE1BQU1DO0lBQ2xELElBQUksRUFDQUMsU0FBUyxFQUNWLEdBQUdGLE1BQ0pHLFFBQVFoQiw4QkFBOEJhLE1BQU1sQztJQUM5QyxNQUFNLENBQUNzQyxZQUFZLEdBQUcsQ0FBQyxHQUFHeEMsUUFBUXlDLGNBQWMsRUFBRTNCLE9BQU80QixNQUFNLENBQUM7UUFDOURDLFNBQVM7SUFDWCxHQUFHSjtJQUNILE1BQU1LLGdCQUFnQixDQUFDLEdBQUc3QyxPQUFPOEMsZ0JBQWdCLEVBQUVDLENBQUFBO1FBQ2pETixZQUFZRixTQUFTLENBQUNRO1FBQ3RCUixhQUFhLE9BQU8sS0FBSyxJQUFJQSxVQUFVUTtJQUN6QztJQUNBLElBQUlwRCxjQUFjNkMsTUFBTVAsSUFBSSxLQUFLTyxNQUFNUSxJQUFJLEtBQUssVUFBVTtRQUN4RCxPQUFvQixXQUFGLEdBQUcsSUFBRzlDLFlBQVkrQyxHQUFHLEVBQUUsS0FBS2xDLE9BQU80QixNQUFNLENBQUM7WUFDMURMLEtBQUtBO1FBQ1AsR0FBR0UsT0FBT0MsYUFBYTtZQUNyQkYsV0FBV007UUFDYjtJQUNGO0lBQ0EsT0FBb0IsV0FBRixHQUFHLElBQUczQyxZQUFZK0MsR0FBRyxFQUFFLEtBQUtsQyxPQUFPNEIsTUFBTSxDQUFDO1FBQzFETCxLQUFLQTtJQUNQLEdBQUdFLE9BQU87UUFDUkQsV0FBV0E7SUFDYjtBQUNGO0FBQ0FKLE9BQU9lLFdBQVcsR0FBRztBQUNyQixJQUFJQyxXQUFXaEI7QUFDZjFDLGtCQUFlLEdBQUcwRCIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9AcmVzdGFydC91aS9janMvQW5jaG9yLmpzPzExM2MiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbmV4cG9ydHMuX19lc01vZHVsZSA9IHRydWU7XG5leHBvcnRzLmlzVHJpdmlhbEhyZWYgPSBpc1RyaXZpYWxIcmVmO1xuZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwO1xudmFyIFJlYWN0ID0gX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQocmVxdWlyZShcInJlYWN0XCIpKTtcbnZhciBfaG9va3MgPSByZXF1aXJlKFwiQHJlc3RhcnQvaG9va3NcIik7XG52YXIgX0J1dHRvbiA9IHJlcXVpcmUoXCIuL0J1dHRvblwiKTtcbnZhciBfanN4UnVudGltZSA9IHJlcXVpcmUoXCJyZWFjdC9qc3gtcnVudGltZVwiKTtcbmNvbnN0IF9leGNsdWRlZCA9IFtcIm9uS2V5RG93blwiXTtcbmZ1bmN0aW9uIF9nZXRSZXF1aXJlV2lsZGNhcmRDYWNoZShub2RlSW50ZXJvcCkgeyBpZiAodHlwZW9mIFdlYWtNYXAgIT09IFwiZnVuY3Rpb25cIikgcmV0dXJuIG51bGw7IHZhciBjYWNoZUJhYmVsSW50ZXJvcCA9IG5ldyBXZWFrTWFwKCk7IHZhciBjYWNoZU5vZGVJbnRlcm9wID0gbmV3IFdlYWtNYXAoKTsgcmV0dXJuIChfZ2V0UmVxdWlyZVdpbGRjYXJkQ2FjaGUgPSBmdW5jdGlvbiAobm9kZUludGVyb3ApIHsgcmV0dXJuIG5vZGVJbnRlcm9wID8gY2FjaGVOb2RlSW50ZXJvcCA6IGNhY2hlQmFiZWxJbnRlcm9wOyB9KShub2RlSW50ZXJvcCk7IH1cbmZ1bmN0aW9uIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkKG9iaiwgbm9kZUludGVyb3ApIHsgaWYgKCFub2RlSW50ZXJvcCAmJiBvYmogJiYgb2JqLl9fZXNNb2R1bGUpIHsgcmV0dXJuIG9iajsgfSBpZiAob2JqID09PSBudWxsIHx8IHR5cGVvZiBvYmogIT09IFwib2JqZWN0XCIgJiYgdHlwZW9mIG9iaiAhPT0gXCJmdW5jdGlvblwiKSB7IHJldHVybiB7IGRlZmF1bHQ6IG9iaiB9OyB9IHZhciBjYWNoZSA9IF9nZXRSZXF1aXJlV2lsZGNhcmRDYWNoZShub2RlSW50ZXJvcCk7IGlmIChjYWNoZSAmJiBjYWNoZS5oYXMob2JqKSkgeyByZXR1cm4gY2FjaGUuZ2V0KG9iaik7IH0gdmFyIG5ld09iaiA9IHt9OyB2YXIgaGFzUHJvcGVydHlEZXNjcmlwdG9yID0gT2JqZWN0LmRlZmluZVByb3BlcnR5ICYmIE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3I7IGZvciAodmFyIGtleSBpbiBvYmopIHsgaWYgKGtleSAhPT0gXCJkZWZhdWx0XCIgJiYgT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKG9iaiwga2V5KSkgeyB2YXIgZGVzYyA9IGhhc1Byb3BlcnR5RGVzY3JpcHRvciA/IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3Iob2JqLCBrZXkpIDogbnVsbDsgaWYgKGRlc2MgJiYgKGRlc2MuZ2V0IHx8IGRlc2Muc2V0KSkgeyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3T2JqLCBrZXksIGRlc2MpOyB9IGVsc2UgeyBuZXdPYmpba2V5XSA9IG9ialtrZXldOyB9IH0gfSBuZXdPYmouZGVmYXVsdCA9IG9iajsgaWYgKGNhY2hlKSB7IGNhY2hlLnNldChvYmosIG5ld09iaik7IH0gcmV0dXJuIG5ld09iajsgfVxuZnVuY3Rpb24gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2Uoc291cmNlLCBleGNsdWRlZCkgeyBpZiAoc291cmNlID09IG51bGwpIHJldHVybiB7fTsgdmFyIHRhcmdldCA9IHt9OyB2YXIgc291cmNlS2V5cyA9IE9iamVjdC5rZXlzKHNvdXJjZSk7IHZhciBrZXksIGk7IGZvciAoaSA9IDA7IGkgPCBzb3VyY2VLZXlzLmxlbmd0aDsgaSsrKSB7IGtleSA9IHNvdXJjZUtleXNbaV07IGlmIChleGNsdWRlZC5pbmRleE9mKGtleSkgPj0gMCkgY29udGludWU7IHRhcmdldFtrZXldID0gc291cmNlW2tleV07IH0gcmV0dXJuIHRhcmdldDsgfVxuZnVuY3Rpb24gaXNUcml2aWFsSHJlZihocmVmKSB7XG4gIHJldHVybiAhaHJlZiB8fCBocmVmLnRyaW0oKSA9PT0gJyMnO1xufVxuLyoqXG4gKiBBbiBnZW5lcmljIGA8YT5gIGNvbXBvbmVudCB0aGF0IGNvdmVycyBhIGZldyBBMTF5IGNhc2VzLCBlbnN1cmluZyB0aGF0XG4gKiBjYXNlcyB3aGVyZSB0aGUgYGhyZWZgIGlzIG1pc3Npbmcgb3IgdHJpdmlhbCBsaWtlIFwiI1wiIGFyZSB0cmVhdGVkIGxpa2UgYnV0dG9ucy5cbiAqL1xuY29uc3QgQW5jaG9yID0gLyojX19QVVJFX18qL1JlYWN0LmZvcndhcmRSZWYoKF9yZWYsIHJlZikgPT4ge1xuICBsZXQge1xuICAgICAgb25LZXlEb3duXG4gICAgfSA9IF9yZWYsXG4gICAgcHJvcHMgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZShfcmVmLCBfZXhjbHVkZWQpO1xuICBjb25zdCBbYnV0dG9uUHJvcHNdID0gKDAsIF9CdXR0b24udXNlQnV0dG9uUHJvcHMpKE9iamVjdC5hc3NpZ24oe1xuICAgIHRhZ05hbWU6ICdhJ1xuICB9LCBwcm9wcykpO1xuICBjb25zdCBoYW5kbGVLZXlEb3duID0gKDAsIF9ob29rcy51c2VFdmVudENhbGxiYWNrKShlID0+IHtcbiAgICBidXR0b25Qcm9wcy5vbktleURvd24oZSk7XG4gICAgb25LZXlEb3duID09IG51bGwgPyB2b2lkIDAgOiBvbktleURvd24oZSk7XG4gIH0pO1xuICBpZiAoaXNUcml2aWFsSHJlZihwcm9wcy5ocmVmKSB8fCBwcm9wcy5yb2xlID09PSAnYnV0dG9uJykge1xuICAgIHJldHVybiAvKiNfX1BVUkVfXyovKDAsIF9qc3hSdW50aW1lLmpzeCkoXCJhXCIsIE9iamVjdC5hc3NpZ24oe1xuICAgICAgcmVmOiByZWZcbiAgICB9LCBwcm9wcywgYnV0dG9uUHJvcHMsIHtcbiAgICAgIG9uS2V5RG93bjogaGFuZGxlS2V5RG93blxuICAgIH0pKTtcbiAgfVxuICByZXR1cm4gLyojX19QVVJFX18qLygwLCBfanN4UnVudGltZS5qc3gpKFwiYVwiLCBPYmplY3QuYXNzaWduKHtcbiAgICByZWY6IHJlZlxuICB9LCBwcm9wcywge1xuICAgIG9uS2V5RG93bjogb25LZXlEb3duXG4gIH0pKTtcbn0pO1xuQW5jaG9yLmRpc3BsYXlOYW1lID0gJ0FuY2hvcic7XG52YXIgX2RlZmF1bHQgPSBBbmNob3I7XG5leHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDsiXSwibmFtZXMiOlsiZXhwb3J0cyIsIl9fZXNNb2R1bGUiLCJpc1RyaXZpYWxIcmVmIiwiZGVmYXVsdCIsIlJlYWN0IiwiX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQiLCJyZXF1aXJlIiwiX2hvb2tzIiwiX0J1dHRvbiIsIl9qc3hSdW50aW1lIiwiX2V4Y2x1ZGVkIiwiX2dldFJlcXVpcmVXaWxkY2FyZENhY2hlIiwibm9kZUludGVyb3AiLCJXZWFrTWFwIiwiY2FjaGVCYWJlbEludGVyb3AiLCJjYWNoZU5vZGVJbnRlcm9wIiwib2JqIiwiY2FjaGUiLCJoYXMiLCJnZXQiLCJuZXdPYmoiLCJoYXNQcm9wZXJ0eURlc2NyaXB0b3IiLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImdldE93blByb3BlcnR5RGVzY3JpcHRvciIsImtleSIsInByb3RvdHlwZSIsImhhc093blByb3BlcnR5IiwiY2FsbCIsImRlc2MiLCJzZXQiLCJfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZSIsInNvdXJjZSIsImV4Y2x1ZGVkIiwidGFyZ2V0Iiwic291cmNlS2V5cyIsImtleXMiLCJpIiwibGVuZ3RoIiwiaW5kZXhPZiIsImhyZWYiLCJ0cmltIiwiQW5jaG9yIiwiZm9yd2FyZFJlZiIsIl9yZWYiLCJyZWYiLCJvbktleURvd24iLCJwcm9wcyIsImJ1dHRvblByb3BzIiwidXNlQnV0dG9uUHJvcHMiLCJhc3NpZ24iLCJ0YWdOYW1lIiwiaGFuZGxlS2V5RG93biIsInVzZUV2ZW50Q2FsbGJhY2siLCJlIiwicm9sZSIsImpzeCIsImRpc3BsYXlOYW1lIiwiX2RlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/Anchor.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/Button.js":
/*!****************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/Button.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports.isTrivialHref = isTrivialHref;\nexports.useButtonProps = useButtonProps;\nexports[\"default\"] = void 0;\nvar React = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar _jsxRuntime = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\nconst _excluded = [\n    \"as\",\n    \"disabled\"\n];\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\nfunction isTrivialHref(href) {\n    return !href || href.trim() === \"#\";\n}\nfunction useButtonProps({ tagName, disabled, href, target, rel, role, onClick, tabIndex = 0, type }) {\n    if (!tagName) {\n        if (href != null || target != null || rel != null) {\n            tagName = \"a\";\n        } else {\n            tagName = \"button\";\n        }\n    }\n    const meta = {\n        tagName\n    };\n    if (tagName === \"button\") {\n        return [\n            {\n                type: type || \"button\",\n                disabled\n            },\n            meta\n        ];\n    }\n    const handleClick = (event)=>{\n        if (disabled || tagName === \"a\" && isTrivialHref(href)) {\n            event.preventDefault();\n        }\n        if (disabled) {\n            event.stopPropagation();\n            return;\n        }\n        onClick == null ? void 0 : onClick(event);\n    };\n    const handleKeyDown = (event)=>{\n        if (event.key === \" \") {\n            event.preventDefault();\n            handleClick(event);\n        }\n    };\n    if (tagName === \"a\") {\n        // Ensure there's a href so Enter can trigger anchor button.\n        href || (href = \"#\");\n        if (disabled) {\n            href = undefined;\n        }\n    }\n    return [\n        {\n            role: role != null ? role : \"button\",\n            // explicitly undefined so that it overrides the props disabled in a spread\n            // e.g. <Tag {...props} {...hookProps} />\n            disabled: undefined,\n            tabIndex: disabled ? undefined : tabIndex,\n            href,\n            target: tagName === \"a\" ? target : undefined,\n            \"aria-disabled\": !disabled ? undefined : disabled,\n            rel: tagName === \"a\" ? rel : undefined,\n            onClick: handleClick,\n            onKeyDown: handleKeyDown\n        },\n        meta\n    ];\n}\nconst Button = /*#__PURE__*/ React.forwardRef((_ref, ref)=>{\n    let { as: asProp, disabled } = _ref, props = _objectWithoutPropertiesLoose(_ref, _excluded);\n    const [buttonProps, { tagName: Component }] = useButtonProps(Object.assign({\n        tagName: asProp,\n        disabled\n    }, props));\n    return /*#__PURE__*/ (0, _jsxRuntime.jsx)(Component, Object.assign({}, props, buttonProps, {\n        ref: ref\n    }));\n});\nButton.displayName = \"Button\";\nvar _default = Button;\nexports[\"default\"] = _default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/Button.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/DataKey.js":
/*!*****************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/DataKey.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nexports.__esModule = true;\nexports.dataAttr = dataAttr;\nexports.dataProp = dataProp;\nexports.PROPERTY_PREFIX = exports.ATTRIBUTE_PREFIX = void 0;\nconst ATTRIBUTE_PREFIX = `data-rr-ui-`;\nexports.ATTRIBUTE_PREFIX = ATTRIBUTE_PREFIX;\nconst PROPERTY_PREFIX = `rrUi`;\nexports.PROPERTY_PREFIX = PROPERTY_PREFIX;\nfunction dataAttr(property) {\n    return `${ATTRIBUTE_PREFIX}${property}`;\n}\nfunction dataProp(property) {\n    return `${PROPERTY_PREFIX}${property}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L3VpL2Nqcy9EYXRhS2V5LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBRWJBLGtCQUFrQixHQUFHO0FBQ3JCQSxnQkFBZ0IsR0FBR0U7QUFDbkJGLGdCQUFnQixHQUFHRztBQUNuQkgsdUJBQXVCLEdBQUdBLHdCQUF3QixHQUFHLEtBQUs7QUFDMUQsTUFBTUssbUJBQW1CLENBQUMsV0FBVyxDQUFDO0FBQ3RDTCx3QkFBd0IsR0FBR0s7QUFDM0IsTUFBTUQsa0JBQWtCLENBQUMsSUFBSSxDQUFDO0FBQzlCSix1QkFBdUIsR0FBR0k7QUFDMUIsU0FBU0YsU0FBU0ksUUFBUTtJQUN4QixPQUFPLENBQUMsRUFBRUQsaUJBQWlCLEVBQUVDLFNBQVMsQ0FBQztBQUN6QztBQUNBLFNBQVNILFNBQVNHLFFBQVE7SUFDeEIsT0FBTyxDQUFDLEVBQUVGLGdCQUFnQixFQUFFRSxTQUFTLENBQUM7QUFDeEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvdWkvY2pzL0RhdGFLZXkuanM/ZWNkNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuZXhwb3J0cy5fX2VzTW9kdWxlID0gdHJ1ZTtcbmV4cG9ydHMuZGF0YUF0dHIgPSBkYXRhQXR0cjtcbmV4cG9ydHMuZGF0YVByb3AgPSBkYXRhUHJvcDtcbmV4cG9ydHMuUFJPUEVSVFlfUFJFRklYID0gZXhwb3J0cy5BVFRSSUJVVEVfUFJFRklYID0gdm9pZCAwO1xuY29uc3QgQVRUUklCVVRFX1BSRUZJWCA9IGBkYXRhLXJyLXVpLWA7XG5leHBvcnRzLkFUVFJJQlVURV9QUkVGSVggPSBBVFRSSUJVVEVfUFJFRklYO1xuY29uc3QgUFJPUEVSVFlfUFJFRklYID0gYHJyVWlgO1xuZXhwb3J0cy5QUk9QRVJUWV9QUkVGSVggPSBQUk9QRVJUWV9QUkVGSVg7XG5mdW5jdGlvbiBkYXRhQXR0cihwcm9wZXJ0eSkge1xuICByZXR1cm4gYCR7QVRUUklCVVRFX1BSRUZJWH0ke3Byb3BlcnR5fWA7XG59XG5mdW5jdGlvbiBkYXRhUHJvcChwcm9wZXJ0eSkge1xuICByZXR1cm4gYCR7UFJPUEVSVFlfUFJFRklYfSR7cHJvcGVydHl9YDtcbn0iXSwibmFtZXMiOlsiZXhwb3J0cyIsIl9fZXNNb2R1bGUiLCJkYXRhQXR0ciIsImRhdGFQcm9wIiwiUFJPUEVSVFlfUFJFRklYIiwiQVRUUklCVVRFX1BSRUZJWCIsInByb3BlcnR5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/DataKey.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/ImperativeTransition.js":
/*!******************************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/ImperativeTransition.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports.useTransition = useTransition;\nexports[\"default\"] = ImperativeTransition;\nexports.renderTransition = renderTransition;\nvar _useMergedRefs = _interopRequireDefault(__webpack_require__(/*! @restart/hooks/useMergedRefs */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergedRefs.js\"));\nvar _useEventCallback = _interopRequireDefault(__webpack_require__(/*! @restart/hooks/useEventCallback */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventCallback.js\"));\nvar _useIsomorphicEffect = _interopRequireDefault(__webpack_require__(/*! @restart/hooks/useIsomorphicEffect */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useIsomorphicEffect.js\"));\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar _NoopTransition = _interopRequireDefault(__webpack_require__(/*! ./NoopTransition */ \"(ssr)/../../node_modules/@restart/ui/cjs/NoopTransition.js\"));\nvar _RTGTransition = _interopRequireDefault(__webpack_require__(/*! ./RTGTransition */ \"(ssr)/../../node_modules/@restart/ui/cjs/RTGTransition.js\"));\nvar _jsxRuntime = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction useTransition({ in: inProp, onTransition }) {\n    const ref = (0, _react.useRef)(null);\n    const isInitialRef = (0, _react.useRef)(true);\n    const handleTransition = (0, _useEventCallback.default)(onTransition);\n    (0, _useIsomorphicEffect.default)(()=>{\n        if (!ref.current) {\n            return undefined;\n        }\n        let stale = false;\n        handleTransition({\n            in: inProp,\n            element: ref.current,\n            initial: isInitialRef.current,\n            isStale: ()=>stale\n        });\n        return ()=>{\n            stale = true;\n        };\n    }, [\n        inProp,\n        handleTransition\n    ]);\n    (0, _useIsomorphicEffect.default)(()=>{\n        isInitialRef.current = false;\n        // this is for strict mode\n        return ()=>{\n            isInitialRef.current = true;\n        };\n    }, []);\n    return ref;\n}\n/**\n * Adapts an imperative transition function to a subset of the RTG `<Transition>` component API.\n *\n * ImperativeTransition does not support mounting options or `appear` at the moment, meaning\n * that it always acts like: `mountOnEnter={true} unmountOnExit={true} appear={true}`\n */ function ImperativeTransition({ children, in: inProp, onExited, onEntered, transition }) {\n    const [exited, setExited] = (0, _react.useState)(!inProp);\n    // TODO: I think this needs to be in an effect\n    if (inProp && exited) {\n        setExited(false);\n    }\n    const ref = useTransition({\n        in: !!inProp,\n        onTransition: (options)=>{\n            const onFinish = ()=>{\n                if (options.isStale()) return;\n                if (options.in) {\n                    onEntered == null ? void 0 : onEntered(options.element, options.initial);\n                } else {\n                    setExited(true);\n                    onExited == null ? void 0 : onExited(options.element);\n                }\n            };\n            Promise.resolve(transition(options)).then(onFinish, (error)=>{\n                if (!options.in) setExited(true);\n                throw error;\n            });\n        }\n    });\n    const combinedRef = (0, _useMergedRefs.default)(ref, children.ref);\n    return exited && !inProp ? null : /*#__PURE__*/ (0, _react.cloneElement)(children, {\n        ref: combinedRef\n    });\n}\nfunction renderTransition(component, runTransition, props) {\n    if (component) {\n        return /*#__PURE__*/ (0, _jsxRuntime.jsx)(_RTGTransition.default, Object.assign({}, props, {\n            component: component\n        }));\n    }\n    if (runTransition) {\n        return /*#__PURE__*/ (0, _jsxRuntime.jsx)(ImperativeTransition, Object.assign({}, props, {\n            transition: runTransition\n        }));\n    }\n    return /*#__PURE__*/ (0, _jsxRuntime.jsx)(_NoopTransition.default, Object.assign({}, props));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/ImperativeTransition.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/Modal.js":
/*!***************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/Modal.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _activeElement = _interopRequireDefault(__webpack_require__(/*! dom-helpers/activeElement */ \"(ssr)/../../node_modules/dom-helpers/esm/activeElement.js\"));\nvar _contains = _interopRequireDefault(__webpack_require__(/*! dom-helpers/contains */ \"(ssr)/../../node_modules/dom-helpers/esm/contains.js\"));\nvar _canUseDOM = _interopRequireDefault(__webpack_require__(/*! dom-helpers/canUseDOM */ \"(ssr)/../../node_modules/dom-helpers/esm/canUseDOM.js\"));\nvar _listen = _interopRequireDefault(__webpack_require__(/*! dom-helpers/listen */ \"(ssr)/../../node_modules/dom-helpers/esm/listen.js\"));\nvar React = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar _reactDom = _interopRequireDefault(__webpack_require__(/*! react-dom */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\"));\nvar _useMounted = _interopRequireDefault(__webpack_require__(/*! @restart/hooks/useMounted */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMounted.js\"));\nvar _useWillUnmount = _interopRequireDefault(__webpack_require__(/*! @restart/hooks/useWillUnmount */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useWillUnmount.js\"));\nvar _usePrevious = _interopRequireDefault(__webpack_require__(/*! @restart/hooks/usePrevious */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/usePrevious.js\"));\nvar _useEventCallback = _interopRequireDefault(__webpack_require__(/*! @restart/hooks/useEventCallback */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventCallback.js\"));\nvar _ModalManager = _interopRequireDefault(__webpack_require__(/*! ./ModalManager */ \"(ssr)/../../node_modules/@restart/ui/cjs/ModalManager.js\"));\nvar _useWaitForDOMRef = _interopRequireDefault(__webpack_require__(/*! ./useWaitForDOMRef */ \"(ssr)/../../node_modules/@restart/ui/cjs/useWaitForDOMRef.js\"));\nvar _useWindow = _interopRequireDefault(__webpack_require__(/*! ./useWindow */ \"(ssr)/../../node_modules/@restart/ui/cjs/useWindow.js\"));\nvar _ImperativeTransition = __webpack_require__(/*! ./ImperativeTransition */ \"(ssr)/../../node_modules/@restart/ui/cjs/ImperativeTransition.js\");\nvar _utils = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/@restart/ui/cjs/utils.js\");\nvar _jsxRuntime = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\nconst _excluded = [\n    \"show\",\n    \"role\",\n    \"className\",\n    \"style\",\n    \"children\",\n    \"backdrop\",\n    \"keyboard\",\n    \"onBackdropClick\",\n    \"onEscapeKeyDown\",\n    \"transition\",\n    \"runTransition\",\n    \"backdropTransition\",\n    \"runBackdropTransition\",\n    \"autoFocus\",\n    \"enforceFocus\",\n    \"restoreFocus\",\n    \"restoreFocusOptions\",\n    \"renderDialog\",\n    \"renderBackdrop\",\n    \"manager\",\n    \"container\",\n    \"onShow\",\n    \"onHide\",\n    \"onExit\",\n    \"onExited\",\n    \"onExiting\",\n    \"onEnter\",\n    \"onEntering\",\n    \"onEntered\"\n];\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\nlet manager;\nfunction getManager(window) {\n    if (!manager) manager = new _ModalManager.default({\n        ownerDocument: window == null ? void 0 : window.document\n    });\n    return manager;\n}\nfunction useModalManager(provided) {\n    const window = (0, _useWindow.default)();\n    const modalManager = provided || getManager(window);\n    const modal = (0, React.useRef)({\n        dialog: null,\n        backdrop: null\n    });\n    return Object.assign(modal.current, {\n        add: ()=>modalManager.add(modal.current),\n        remove: ()=>modalManager.remove(modal.current),\n        isTopModal: ()=>modalManager.isTopModal(modal.current),\n        setDialogRef: (0, React.useCallback)((ref)=>{\n            modal.current.dialog = ref;\n        }, []),\n        setBackdropRef: (0, React.useCallback)((ref)=>{\n            modal.current.backdrop = ref;\n        }, [])\n    });\n}\nconst Modal = /*#__PURE__*/ (0, React.forwardRef)((_ref, ref)=>{\n    let { show = false, role = \"dialog\", className, style, children, backdrop = true, keyboard = true, onBackdropClick, onEscapeKeyDown, transition, runTransition, backdropTransition, runBackdropTransition, autoFocus = true, enforceFocus = true, restoreFocus = true, restoreFocusOptions, renderDialog, renderBackdrop = (props)=>/*#__PURE__*/ (0, _jsxRuntime.jsx)(\"div\", Object.assign({}, props)), manager: providedManager, container: containerRef, onShow, onHide = ()=>{}, onExit, onExited, onExiting, onEnter, onEntering, onEntered } = _ref, rest = _objectWithoutPropertiesLoose(_ref, _excluded);\n    const ownerWindow = (0, _useWindow.default)();\n    const container = (0, _useWaitForDOMRef.default)(containerRef);\n    const modal = useModalManager(providedManager);\n    const isMounted = (0, _useMounted.default)();\n    const prevShow = (0, _usePrevious.default)(show);\n    const [exited, setExited] = (0, React.useState)(!show);\n    const lastFocusRef = (0, React.useRef)(null);\n    (0, React.useImperativeHandle)(ref, ()=>modal, [\n        modal\n    ]);\n    if (_canUseDOM.default && !prevShow && show) {\n        lastFocusRef.current = (0, _activeElement.default)(ownerWindow == null ? void 0 : ownerWindow.document);\n    }\n    // TODO: I think this needs to be in an effect\n    if (show && exited) {\n        setExited(false);\n    }\n    const handleShow = (0, _useEventCallback.default)(()=>{\n        modal.add();\n        removeKeydownListenerRef.current = (0, _listen.default)(document, \"keydown\", handleDocumentKeyDown);\n        removeFocusListenerRef.current = (0, _listen.default)(document, \"focus\", // the timeout is necessary b/c this will run before the new modal is mounted\n        // and so steals focus from it\n        ()=>setTimeout(handleEnforceFocus), true);\n        if (onShow) {\n            onShow();\n        }\n        // autofocus after onShow to not trigger a focus event for previous\n        // modals before this one is shown.\n        if (autoFocus) {\n            var _modal$dialog$ownerDo, _modal$dialog;\n            const currentActiveElement = (0, _activeElement.default)((_modal$dialog$ownerDo = (_modal$dialog = modal.dialog) == null ? void 0 : _modal$dialog.ownerDocument) != null ? _modal$dialog$ownerDo : ownerWindow == null ? void 0 : ownerWindow.document);\n            if (modal.dialog && currentActiveElement && !(0, _contains.default)(modal.dialog, currentActiveElement)) {\n                lastFocusRef.current = currentActiveElement;\n                modal.dialog.focus();\n            }\n        }\n    });\n    const handleHide = (0, _useEventCallback.default)(()=>{\n        modal.remove();\n        removeKeydownListenerRef.current == null ? void 0 : removeKeydownListenerRef.current();\n        removeFocusListenerRef.current == null ? void 0 : removeFocusListenerRef.current();\n        if (restoreFocus) {\n            var _lastFocusRef$current;\n            // Support: <=IE11 doesn't support `focus()` on svg elements (RB: #917)\n            (_lastFocusRef$current = lastFocusRef.current) == null ? void 0 : _lastFocusRef$current.focus == null ? void 0 : _lastFocusRef$current.focus(restoreFocusOptions);\n            lastFocusRef.current = null;\n        }\n    });\n    // TODO: try and combine these effects: https://github.com/react-bootstrap/react-overlays/pull/794#discussion_r409954120\n    // Show logic when:\n    //  - show is `true` _and_ `container` has resolved\n    (0, React.useEffect)(()=>{\n        if (!show || !container) return;\n        handleShow();\n    }, [\n        show,\n        container,\n        /* should never change: */ handleShow\n    ]);\n    // Hide cleanup logic when:\n    //  - `exited` switches to true\n    //  - component unmounts;\n    (0, React.useEffect)(()=>{\n        if (!exited) return;\n        handleHide();\n    }, [\n        exited,\n        handleHide\n    ]);\n    (0, _useWillUnmount.default)(()=>{\n        handleHide();\n    });\n    // --------------------------------\n    const handleEnforceFocus = (0, _useEventCallback.default)(()=>{\n        if (!enforceFocus || !isMounted() || !modal.isTopModal()) {\n            return;\n        }\n        const currentActiveElement = (0, _activeElement.default)(ownerWindow == null ? void 0 : ownerWindow.document);\n        if (modal.dialog && currentActiveElement && !(0, _contains.default)(modal.dialog, currentActiveElement)) {\n            modal.dialog.focus();\n        }\n    });\n    const handleBackdropClick = (0, _useEventCallback.default)((e)=>{\n        if (e.target !== e.currentTarget) {\n            return;\n        }\n        onBackdropClick == null ? void 0 : onBackdropClick(e);\n        if (backdrop === true) {\n            onHide();\n        }\n    });\n    const handleDocumentKeyDown = (0, _useEventCallback.default)((e)=>{\n        if (keyboard && (0, _utils.isEscKey)(e) && modal.isTopModal()) {\n            onEscapeKeyDown == null ? void 0 : onEscapeKeyDown(e);\n            if (!e.defaultPrevented) {\n                onHide();\n            }\n        }\n    });\n    const removeFocusListenerRef = (0, React.useRef)();\n    const removeKeydownListenerRef = (0, React.useRef)();\n    const handleHidden = (...args)=>{\n        setExited(true);\n        onExited == null ? void 0 : onExited(...args);\n    };\n    if (!container) {\n        return null;\n    }\n    const dialogProps = Object.assign({\n        role,\n        ref: modal.setDialogRef,\n        // apparently only works on the dialog role element\n        \"aria-modal\": role === \"dialog\" ? true : undefined\n    }, rest, {\n        style,\n        className,\n        tabIndex: -1\n    });\n    let dialog = renderDialog ? renderDialog(dialogProps) : /*#__PURE__*/ (0, _jsxRuntime.jsx)(\"div\", Object.assign({}, dialogProps, {\n        children: /*#__PURE__*/ React.cloneElement(children, {\n            role: \"document\"\n        })\n    }));\n    dialog = (0, _ImperativeTransition.renderTransition)(transition, runTransition, {\n        unmountOnExit: true,\n        mountOnEnter: true,\n        appear: true,\n        in: !!show,\n        onExit,\n        onExiting,\n        onExited: handleHidden,\n        onEnter,\n        onEntering,\n        onEntered,\n        children: dialog\n    });\n    let backdropElement = null;\n    if (backdrop) {\n        backdropElement = renderBackdrop({\n            ref: modal.setBackdropRef,\n            onClick: handleBackdropClick\n        });\n        backdropElement = (0, _ImperativeTransition.renderTransition)(backdropTransition, runBackdropTransition, {\n            in: !!show,\n            appear: true,\n            mountOnEnter: true,\n            unmountOnExit: true,\n            children: backdropElement\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {\n        children: /*#__PURE__*/ _reactDom.default.createPortal(/*#__PURE__*/ (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {\n            children: [\n                backdropElement,\n                dialog\n            ]\n        }), container)\n    });\n});\nModal.displayName = \"Modal\";\nvar _default = Object.assign(Modal, {\n    Manager: _ModalManager.default\n});\nexports[\"default\"] = _default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/Modal.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/ModalManager.js":
/*!**********************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/ModalManager.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = exports.OPEN_DATA_ATTRIBUTE = void 0;\nvar _css = _interopRequireDefault(__webpack_require__(/*! dom-helpers/css */ \"(ssr)/../../node_modules/dom-helpers/esm/css.js\"));\nvar _DataKey = __webpack_require__(/*! ./DataKey */ \"(ssr)/../../node_modules/@restart/ui/cjs/DataKey.js\");\nvar _getScrollbarWidth = _interopRequireDefault(__webpack_require__(/*! ./getScrollbarWidth */ \"(ssr)/../../node_modules/@restart/ui/cjs/getScrollbarWidth.js\"));\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nconst OPEN_DATA_ATTRIBUTE = (0, _DataKey.dataAttr)(\"modal-open\");\n/**\n * Manages a stack of Modals as well as ensuring\n * body scrolling is is disabled and padding accounted for\n */ exports.OPEN_DATA_ATTRIBUTE = OPEN_DATA_ATTRIBUTE;\nclass ModalManager {\n    constructor({ ownerDocument, handleContainerOverflow = true, isRTL = false } = {}){\n        this.handleContainerOverflow = handleContainerOverflow;\n        this.isRTL = isRTL;\n        this.modals = [];\n        this.ownerDocument = ownerDocument;\n    }\n    getScrollbarWidth() {\n        return (0, _getScrollbarWidth.default)(this.ownerDocument);\n    }\n    getElement() {\n        return (this.ownerDocument || document).body;\n    }\n    setModalAttributes(_modal) {\n    // For overriding\n    }\n    removeModalAttributes(_modal) {\n    // For overriding\n    }\n    setContainerStyle(containerState) {\n        const style = {\n            overflow: \"hidden\"\n        };\n        // we are only interested in the actual `style` here\n        // because we will override it\n        const paddingProp = this.isRTL ? \"paddingLeft\" : \"paddingRight\";\n        const container = this.getElement();\n        containerState.style = {\n            overflow: container.style.overflow,\n            [paddingProp]: container.style[paddingProp]\n        };\n        if (containerState.scrollBarWidth) {\n            // use computed style, here to get the real padding\n            // to add our scrollbar width\n            style[paddingProp] = `${parseInt((0, _css.default)(container, paddingProp) || \"0\", 10) + containerState.scrollBarWidth}px`;\n        }\n        container.setAttribute(OPEN_DATA_ATTRIBUTE, \"\");\n        (0, _css.default)(container, style);\n    }\n    reset() {\n        [\n            ...this.modals\n        ].forEach((m)=>this.remove(m));\n    }\n    removeContainerStyle(containerState) {\n        const container = this.getElement();\n        container.removeAttribute(OPEN_DATA_ATTRIBUTE);\n        Object.assign(container.style, containerState.style);\n    }\n    add(modal) {\n        let modalIdx = this.modals.indexOf(modal);\n        if (modalIdx !== -1) {\n            return modalIdx;\n        }\n        modalIdx = this.modals.length;\n        this.modals.push(modal);\n        this.setModalAttributes(modal);\n        if (modalIdx !== 0) {\n            return modalIdx;\n        }\n        this.state = {\n            scrollBarWidth: this.getScrollbarWidth(),\n            style: {}\n        };\n        if (this.handleContainerOverflow) {\n            this.setContainerStyle(this.state);\n        }\n        return modalIdx;\n    }\n    remove(modal) {\n        const modalIdx = this.modals.indexOf(modal);\n        if (modalIdx === -1) {\n            return;\n        }\n        this.modals.splice(modalIdx, 1);\n        // if that was the last modal in a container,\n        // clean up the container\n        if (!this.modals.length && this.handleContainerOverflow) {\n            this.removeContainerStyle(this.state);\n        }\n        this.removeModalAttributes(modal);\n    }\n    isTopModal(modal) {\n        return !!this.modals.length && this.modals[this.modals.length - 1] === modal;\n    }\n}\nvar _default = ModalManager;\nexports[\"default\"] = _default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/ModalManager.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/Nav.js":
/*!*************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/Nav.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _querySelectorAll = _interopRequireDefault(__webpack_require__(/*! dom-helpers/querySelectorAll */ \"(ssr)/../../node_modules/dom-helpers/esm/querySelectorAll.js\"));\nvar React = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar _useForceUpdate = _interopRequireDefault(__webpack_require__(/*! @restart/hooks/useForceUpdate */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useForceUpdate.js\"));\nvar _useMergedRefs = _interopRequireDefault(__webpack_require__(/*! @restart/hooks/useMergedRefs */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergedRefs.js\"));\nvar _NavContext = _interopRequireDefault(__webpack_require__(/*! ./NavContext */ \"(ssr)/../../node_modules/@restart/ui/cjs/NavContext.js\"));\nvar _SelectableContext = _interopRequireWildcard(__webpack_require__(/*! ./SelectableContext */ \"(ssr)/../../node_modules/@restart/ui/cjs/SelectableContext.js\"));\nvar _TabContext = _interopRequireDefault(__webpack_require__(/*! ./TabContext */ \"(ssr)/../../node_modules/@restart/ui/cjs/TabContext.js\"));\nvar _DataKey = __webpack_require__(/*! ./DataKey */ \"(ssr)/../../node_modules/@restart/ui/cjs/DataKey.js\");\nvar _NavItem = _interopRequireDefault(__webpack_require__(/*! ./NavItem */ \"(ssr)/../../node_modules/@restart/ui/cjs/NavItem.js\"));\nvar _jsxRuntime = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\nconst _excluded = [\n    \"as\",\n    \"onSelect\",\n    \"activeKey\",\n    \"role\",\n    \"onKeyDown\"\n];\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nconst noop = ()=>{};\nconst EVENT_KEY_ATTR = (0, _DataKey.dataAttr)(\"event-key\");\nconst Nav = /*#__PURE__*/ React.forwardRef((_ref, ref)=>{\n    let { // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = \"div\", onSelect, activeKey, role, onKeyDown } = _ref, props = _objectWithoutPropertiesLoose(_ref, _excluded);\n    // A ref and forceUpdate for refocus, b/c we only want to trigger when needed\n    // and don't want to reset the set in the effect\n    const forceUpdate = (0, _useForceUpdate.default)();\n    const needsRefocusRef = (0, React.useRef)(false);\n    const parentOnSelect = (0, React.useContext)(_SelectableContext.default);\n    const tabContext = (0, React.useContext)(_TabContext.default);\n    let getControlledId, getControllerId;\n    if (tabContext) {\n        role = role || \"tablist\";\n        activeKey = tabContext.activeKey;\n        // TODO: do we need to duplicate these?\n        getControlledId = tabContext.getControlledId;\n        getControllerId = tabContext.getControllerId;\n    }\n    const listNode = (0, React.useRef)(null);\n    const getNextActiveTab = (offset)=>{\n        const currentListNode = listNode.current;\n        if (!currentListNode) return null;\n        const items = (0, _querySelectorAll.default)(currentListNode, `[${EVENT_KEY_ATTR}]:not([aria-disabled=true])`);\n        const activeChild = currentListNode.querySelector(\"[aria-selected=true]\");\n        if (!activeChild || activeChild !== document.activeElement) return null;\n        const index = items.indexOf(activeChild);\n        if (index === -1) return null;\n        let nextIndex = index + offset;\n        if (nextIndex >= items.length) nextIndex = 0;\n        if (nextIndex < 0) nextIndex = items.length - 1;\n        return items[nextIndex];\n    };\n    const handleSelect = (key, event)=>{\n        if (key == null) return;\n        onSelect == null ? void 0 : onSelect(key, event);\n        parentOnSelect == null ? void 0 : parentOnSelect(key, event);\n    };\n    const handleKeyDown = (event)=>{\n        onKeyDown == null ? void 0 : onKeyDown(event);\n        if (!tabContext) {\n            return;\n        }\n        let nextActiveChild;\n        switch(event.key){\n            case \"ArrowLeft\":\n            case \"ArrowUp\":\n                nextActiveChild = getNextActiveTab(-1);\n                break;\n            case \"ArrowRight\":\n            case \"ArrowDown\":\n                nextActiveChild = getNextActiveTab(1);\n                break;\n            default:\n                return;\n        }\n        if (!nextActiveChild) return;\n        event.preventDefault();\n        handleSelect(nextActiveChild.dataset[(0, _DataKey.dataProp)(\"EventKey\")] || null, event);\n        needsRefocusRef.current = true;\n        forceUpdate();\n    };\n    (0, React.useEffect)(()=>{\n        if (listNode.current && needsRefocusRef.current) {\n            const activeChild = listNode.current.querySelector(`[${EVENT_KEY_ATTR}][aria-selected=true]`);\n            activeChild == null ? void 0 : activeChild.focus();\n        }\n        needsRefocusRef.current = false;\n    });\n    const mergedRef = (0, _useMergedRefs.default)(ref, listNode);\n    return /*#__PURE__*/ (0, _jsxRuntime.jsx)(_SelectableContext.default.Provider, {\n        value: handleSelect,\n        children: /*#__PURE__*/ (0, _jsxRuntime.jsx)(_NavContext.default.Provider, {\n            value: {\n                role,\n                // used by NavLink to determine it's role\n                activeKey: (0, _SelectableContext.makeEventKey)(activeKey),\n                getControlledId: getControlledId || noop,\n                getControllerId: getControllerId || noop\n            },\n            children: /*#__PURE__*/ (0, _jsxRuntime.jsx)(Component, Object.assign({}, props, {\n                onKeyDown: handleKeyDown,\n                ref: mergedRef,\n                role: role\n            }))\n        })\n    });\n});\nNav.displayName = \"Nav\";\nvar _default = Object.assign(Nav, {\n    Item: _NavItem.default\n});\nexports[\"default\"] = _default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/Nav.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/NavContext.js":
/*!********************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/NavContext.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar React = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nconst NavContext = /*#__PURE__*/ React.createContext(null);\nNavContext.displayName = \"NavContext\";\nvar _default = NavContext;\nexports[\"default\"] = _default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/NavContext.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/NavItem.js":
/*!*****************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/NavItem.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports.useNavItem = useNavItem;\nexports[\"default\"] = void 0;\nvar React = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar _useEventCallback = _interopRequireDefault(__webpack_require__(/*! @restart/hooks/useEventCallback */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventCallback.js\"));\nvar _NavContext = _interopRequireDefault(__webpack_require__(/*! ./NavContext */ \"(ssr)/../../node_modules/@restart/ui/cjs/NavContext.js\"));\nvar _SelectableContext = _interopRequireWildcard(__webpack_require__(/*! ./SelectableContext */ \"(ssr)/../../node_modules/@restart/ui/cjs/SelectableContext.js\"));\nvar _Button = _interopRequireDefault(__webpack_require__(/*! ./Button */ \"(ssr)/../../node_modules/@restart/ui/cjs/Button.js\"));\nvar _DataKey = __webpack_require__(/*! ./DataKey */ \"(ssr)/../../node_modules/@restart/ui/cjs/DataKey.js\");\nvar _TabContext = _interopRequireDefault(__webpack_require__(/*! ./TabContext */ \"(ssr)/../../node_modules/@restart/ui/cjs/TabContext.js\"));\nvar _jsxRuntime = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\nconst _excluded = [\n    \"as\",\n    \"active\",\n    \"eventKey\"\n];\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\nfunction useNavItem({ key, onClick, active, id, role, disabled }) {\n    const parentOnSelect = (0, React.useContext)(_SelectableContext.default);\n    const navContext = (0, React.useContext)(_NavContext.default);\n    const tabContext = (0, React.useContext)(_TabContext.default);\n    let isActive = active;\n    const props = {\n        role\n    };\n    if (navContext) {\n        if (!role && navContext.role === \"tablist\") props.role = \"tab\";\n        const contextControllerId = navContext.getControllerId(key != null ? key : null);\n        const contextControlledId = navContext.getControlledId(key != null ? key : null);\n        // @ts-ignore\n        props[(0, _DataKey.dataAttr)(\"event-key\")] = key;\n        props.id = contextControllerId || id;\n        isActive = active == null && key != null ? navContext.activeKey === key : active;\n        /**\n     * Simplified scenario for `mountOnEnter`.\n     *\n     * While it would make sense to keep 'aria-controls' for tabs that have been mounted at least\n     * once, it would also complicate the code quite a bit, for very little gain.\n     * The following implementation is probably good enough.\n     *\n     * @see https://github.com/react-restart/ui/pull/40#issuecomment-1009971561\n     */ if (isActive || !(tabContext != null && tabContext.unmountOnExit) && !(tabContext != null && tabContext.mountOnEnter)) props[\"aria-controls\"] = contextControlledId;\n    }\n    if (props.role === \"tab\") {\n        props[\"aria-selected\"] = isActive;\n        if (!isActive) {\n            props.tabIndex = -1;\n        }\n        if (disabled) {\n            props.tabIndex = -1;\n            props[\"aria-disabled\"] = true;\n        }\n    }\n    props.onClick = (0, _useEventCallback.default)((e)=>{\n        if (disabled) return;\n        onClick == null ? void 0 : onClick(e);\n        if (key == null) {\n            return;\n        }\n        if (parentOnSelect && !e.isPropagationStopped()) {\n            parentOnSelect(key, e);\n        }\n    });\n    return [\n        props,\n        {\n            isActive\n        }\n    ];\n}\nconst NavItem = /*#__PURE__*/ React.forwardRef((_ref, ref)=>{\n    let { as: Component = _Button.default, active, eventKey } = _ref, options = _objectWithoutPropertiesLoose(_ref, _excluded);\n    const [props, meta] = useNavItem(Object.assign({\n        key: (0, _SelectableContext.makeEventKey)(eventKey, options.href),\n        active\n    }, options));\n    // @ts-ignore\n    props[(0, _DataKey.dataAttr)(\"active\")] = meta.isActive;\n    return /*#__PURE__*/ (0, _jsxRuntime.jsx)(Component, Object.assign({}, options, props, {\n        ref: ref\n    }));\n});\nNavItem.displayName = \"NavItem\";\nvar _default = NavItem;\nexports[\"default\"] = _default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/NavItem.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/NoopTransition.js":
/*!************************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/NoopTransition.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _useEventCallback = _interopRequireDefault(__webpack_require__(/*! @restart/hooks/useEventCallback */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventCallback.js\"));\nvar _useMergedRefs = _interopRequireDefault(__webpack_require__(/*! @restart/hooks/useMergedRefs */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergedRefs.js\"));\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction NoopTransition({ children, in: inProp, onExited, mountOnEnter, unmountOnExit }) {\n    const ref = (0, _react.useRef)(null);\n    const hasEnteredRef = (0, _react.useRef)(inProp);\n    const handleExited = (0, _useEventCallback.default)(onExited);\n    (0, _react.useEffect)(()=>{\n        if (inProp) hasEnteredRef.current = true;\n        else {\n            handleExited(ref.current);\n        }\n    }, [\n        inProp,\n        handleExited\n    ]);\n    const combinedRef = (0, _useMergedRefs.default)(ref, children.ref);\n    const child = /*#__PURE__*/ (0, _react.cloneElement)(children, {\n        ref: combinedRef\n    });\n    if (inProp) return child;\n    if (unmountOnExit) {\n        return null;\n    }\n    if (!hasEnteredRef.current && mountOnEnter) {\n        return null;\n    }\n    return child;\n}\nvar _default = NoopTransition;\nexports[\"default\"] = _default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/NoopTransition.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/RTGTransition.js":
/*!***********************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/RTGTransition.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar React = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nvar _useRTGTransitionProps = _interopRequireDefault(__webpack_require__(/*! ./useRTGTransitionProps */ \"(ssr)/../../node_modules/@restart/ui/cjs/useRTGTransitionProps.js\"));\nvar _jsxRuntime = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\nconst _excluded = [\n    \"component\"\n];\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\n// Normalizes Transition callbacks when nodeRef is used.\nconst RTGTransition = /*#__PURE__*/ React.forwardRef((_ref, ref)=>{\n    let { component: Component } = _ref, props = _objectWithoutPropertiesLoose(_ref, _excluded);\n    const transitionProps = (0, _useRTGTransitionProps.default)(props);\n    return /*#__PURE__*/ (0, _jsxRuntime.jsx)(Component, Object.assign({\n        ref: ref\n    }, transitionProps));\n});\nvar _default = RTGTransition;\nexports[\"default\"] = _default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/RTGTransition.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/SelectableContext.js":
/*!***************************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/SelectableContext.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = exports.makeEventKey = void 0;\nvar React = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nconst SelectableContext = /*#__PURE__*/ React.createContext(null);\nconst makeEventKey = (eventKey, href = null)=>{\n    if (eventKey != null) return String(eventKey);\n    return href || null;\n};\nexports.makeEventKey = makeEventKey;\nvar _default = SelectableContext;\nexports[\"default\"] = _default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/SelectableContext.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/TabContext.js":
/*!********************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/TabContext.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar React = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nconst TabContext = /*#__PURE__*/ React.createContext(null);\nvar _default = TabContext;\nexports[\"default\"] = _default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/TabContext.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/getScrollbarWidth.js":
/*!***************************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/getScrollbarWidth.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = getBodyScrollbarWidth;\n/**\n * Get the width of the vertical window scrollbar if it's visible\n */ function getBodyScrollbarWidth(ownerDocument = document) {\n    const window = ownerDocument.defaultView;\n    return Math.abs(window.innerWidth - ownerDocument.documentElement.clientWidth);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L3VpL2Nqcy9nZXRTY3JvbGxiYXJXaWR0aC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUViQSxrQkFBa0IsR0FBRztBQUNyQkEsa0JBQWUsR0FBR0c7QUFDbEI7O0NBRUMsR0FDRCxTQUFTQSxzQkFBc0JDLGdCQUFnQkMsUUFBUTtJQUNyRCxNQUFNQyxTQUFTRixjQUFjRyxXQUFXO0lBQ3hDLE9BQU9DLEtBQUtDLEdBQUcsQ0FBQ0gsT0FBT0ksVUFBVSxHQUFHTixjQUFjTyxlQUFlLENBQUNDLFdBQVc7QUFDL0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvdWkvY2pzL2dldFNjcm9sbGJhcldpZHRoLmpzPzhjMzkiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbmV4cG9ydHMuX19lc01vZHVsZSA9IHRydWU7XG5leHBvcnRzLmRlZmF1bHQgPSBnZXRCb2R5U2Nyb2xsYmFyV2lkdGg7XG4vKipcbiAqIEdldCB0aGUgd2lkdGggb2YgdGhlIHZlcnRpY2FsIHdpbmRvdyBzY3JvbGxiYXIgaWYgaXQncyB2aXNpYmxlXG4gKi9cbmZ1bmN0aW9uIGdldEJvZHlTY3JvbGxiYXJXaWR0aChvd25lckRvY3VtZW50ID0gZG9jdW1lbnQpIHtcbiAgY29uc3Qgd2luZG93ID0gb3duZXJEb2N1bWVudC5kZWZhdWx0VmlldztcbiAgcmV0dXJuIE1hdGguYWJzKHdpbmRvdy5pbm5lcldpZHRoIC0gb3duZXJEb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xpZW50V2lkdGgpO1xufSJdLCJuYW1lcyI6WyJleHBvcnRzIiwiX19lc01vZHVsZSIsImRlZmF1bHQiLCJnZXRCb2R5U2Nyb2xsYmFyV2lkdGgiLCJvd25lckRvY3VtZW50IiwiZG9jdW1lbnQiLCJ3aW5kb3ciLCJkZWZhdWx0VmlldyIsIk1hdGgiLCJhYnMiLCJpbm5lcldpZHRoIiwiZG9jdW1lbnRFbGVtZW50IiwiY2xpZW50V2lkdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/getScrollbarWidth.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/useRTGTransitionProps.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/useRTGTransitionProps.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = useRTGTransitionProps;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar _useMergedRefs = _interopRequireDefault(__webpack_require__(/*! @restart/hooks/useMergedRefs */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergedRefs.js\"));\nvar _utils = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/@restart/ui/cjs/utils.js\");\nconst _excluded = [\n    \"onEnter\",\n    \"onEntering\",\n    \"onEntered\",\n    \"onExit\",\n    \"onExiting\",\n    \"onExited\",\n    \"addEndListener\",\n    \"children\"\n];\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\n/**\n * Normalizes RTG transition callbacks with nodeRef to better support\n * strict mode.\n *\n * @param props Transition props.\n * @returns Normalized transition props.\n */ function useRTGTransitionProps(_ref) {\n    let { onEnter, onEntering, onEntered, onExit, onExiting, onExited, addEndListener, children } = _ref, props = _objectWithoutPropertiesLoose(_ref, _excluded);\n    const { major } = (0, _utils.getReactVersion)();\n    const childRef = major >= 19 ? children.props.ref : children.ref;\n    const nodeRef = (0, _react.useRef)(null);\n    const mergedRef = (0, _useMergedRefs.default)(nodeRef, typeof children === \"function\" ? null : childRef);\n    const normalize = (callback)=>(param)=>{\n            if (callback && nodeRef.current) {\n                callback(nodeRef.current, param);\n            }\n        };\n    /* eslint-disable react-hooks/exhaustive-deps */ const handleEnter = (0, _react.useCallback)(normalize(onEnter), [\n        onEnter\n    ]);\n    const handleEntering = (0, _react.useCallback)(normalize(onEntering), [\n        onEntering\n    ]);\n    const handleEntered = (0, _react.useCallback)(normalize(onEntered), [\n        onEntered\n    ]);\n    const handleExit = (0, _react.useCallback)(normalize(onExit), [\n        onExit\n    ]);\n    const handleExiting = (0, _react.useCallback)(normalize(onExiting), [\n        onExiting\n    ]);\n    const handleExited = (0, _react.useCallback)(normalize(onExited), [\n        onExited\n    ]);\n    const handleAddEndListener = (0, _react.useCallback)(normalize(addEndListener), [\n        addEndListener\n    ]);\n    /* eslint-enable react-hooks/exhaustive-deps */ return Object.assign({}, props, {\n        nodeRef\n    }, onEnter && {\n        onEnter: handleEnter\n    }, onEntering && {\n        onEntering: handleEntering\n    }, onEntered && {\n        onEntered: handleEntered\n    }, onExit && {\n        onExit: handleExit\n    }, onExiting && {\n        onExiting: handleExiting\n    }, onExited && {\n        onExited: handleExited\n    }, addEndListener && {\n        addEndListener: handleAddEndListener\n    }, {\n        children: typeof children === \"function\" ? (status, innerProps)=>// TODO: Types for RTG missing innerProps, so need to cast.\n            children(status, Object.assign({}, innerProps, {\n                ref: mergedRef\n            })) : /*#__PURE__*/ (0, _react.cloneElement)(children, {\n            ref: mergedRef\n        })\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L3VpL2Nqcy91c2VSVEdUcmFuc2l0aW9uUHJvcHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFFYkEsa0JBQWtCLEdBQUc7QUFDckJBLGtCQUFlLEdBQUdHO0FBQ2xCLElBQUlDLFNBQVNDLG1CQUFPQSxDQUFDLDRHQUFPO0FBQzVCLElBQUlDLGlCQUFpQkMsdUJBQXVCRixtQkFBT0EsQ0FBQywySEFBOEI7QUFDbEYsSUFBSUcsU0FBU0gsbUJBQU9BLENBQUMsa0VBQVM7QUFDOUIsTUFBTUksWUFBWTtJQUFDO0lBQVc7SUFBYztJQUFhO0lBQVU7SUFBYTtJQUFZO0lBQWtCO0NBQVc7QUFDekgsU0FBU0YsdUJBQXVCRyxHQUFHO0lBQUksT0FBT0EsT0FBT0EsSUFBSVQsVUFBVSxHQUFHUyxNQUFNO1FBQUVSLFNBQVNRO0lBQUk7QUFBRztBQUM5RixTQUFTQyw4QkFBOEJDLE1BQU0sRUFBRUMsUUFBUTtJQUFJLElBQUlELFVBQVUsTUFBTSxPQUFPLENBQUM7SUFBRyxJQUFJRSxTQUFTLENBQUM7SUFBRyxJQUFJQyxhQUFhQyxPQUFPQyxJQUFJLENBQUNMO0lBQVMsSUFBSU0sS0FBS0M7SUFBRyxJQUFLQSxJQUFJLEdBQUdBLElBQUlKLFdBQVdLLE1BQU0sRUFBRUQsSUFBSztRQUFFRCxNQUFNSCxVQUFVLENBQUNJLEVBQUU7UUFBRSxJQUFJTixTQUFTUSxPQUFPLENBQUNILFFBQVEsR0FBRztRQUFVSixNQUFNLENBQUNJLElBQUksR0FBR04sTUFBTSxDQUFDTSxJQUFJO0lBQUU7SUFBRSxPQUFPSjtBQUFRO0FBQ2xUOzs7Ozs7Q0FNQyxHQUNELFNBQVNYLHNCQUFzQm1CLElBQUk7SUFDakMsSUFBSSxFQUNBQyxPQUFPLEVBQ1BDLFVBQVUsRUFDVkMsU0FBUyxFQUNUQyxNQUFNLEVBQ05DLFNBQVMsRUFDVEMsUUFBUSxFQUNSQyxjQUFjLEVBQ2RDLFFBQVEsRUFDVCxHQUFHUixNQUNKUyxRQUFRcEIsOEJBQThCVyxNQUFNYjtJQUM5QyxNQUFNLEVBQ0p1QixLQUFLLEVBQ04sR0FBRyxDQUFDLEdBQUd4QixPQUFPeUIsZUFBZTtJQUM5QixNQUFNQyxXQUFXRixTQUFTLEtBQUtGLFNBQVNDLEtBQUssQ0FBQ0ksR0FBRyxHQUFHTCxTQUFTSyxHQUFHO0lBQ2hFLE1BQU1DLFVBQVUsQ0FBQyxHQUFHaEMsT0FBT2lDLE1BQU0sRUFBRTtJQUNuQyxNQUFNQyxZQUFZLENBQUMsR0FBR2hDLGVBQWVKLE9BQU8sRUFBRWtDLFNBQVMsT0FBT04sYUFBYSxhQUFhLE9BQU9JO0lBQy9GLE1BQU1LLFlBQVlDLENBQUFBLFdBQVlDLENBQUFBO1lBQzVCLElBQUlELFlBQVlKLFFBQVFNLE9BQU8sRUFBRTtnQkFDL0JGLFNBQVNKLFFBQVFNLE9BQU8sRUFBRUQ7WUFDNUI7UUFDRjtJQUVBLDhDQUE4QyxHQUM5QyxNQUFNRSxjQUFjLENBQUMsR0FBR3ZDLE9BQU93QyxXQUFXLEVBQUVMLFVBQVVoQixVQUFVO1FBQUNBO0tBQVE7SUFDekUsTUFBTXNCLGlCQUFpQixDQUFDLEdBQUd6QyxPQUFPd0MsV0FBVyxFQUFFTCxVQUFVZixhQUFhO1FBQUNBO0tBQVc7SUFDbEYsTUFBTXNCLGdCQUFnQixDQUFDLEdBQUcxQyxPQUFPd0MsV0FBVyxFQUFFTCxVQUFVZCxZQUFZO1FBQUNBO0tBQVU7SUFDL0UsTUFBTXNCLGFBQWEsQ0FBQyxHQUFHM0MsT0FBT3dDLFdBQVcsRUFBRUwsVUFBVWIsU0FBUztRQUFDQTtLQUFPO0lBQ3RFLE1BQU1zQixnQkFBZ0IsQ0FBQyxHQUFHNUMsT0FBT3dDLFdBQVcsRUFBRUwsVUFBVVosWUFBWTtRQUFDQTtLQUFVO0lBQy9FLE1BQU1zQixlQUFlLENBQUMsR0FBRzdDLE9BQU93QyxXQUFXLEVBQUVMLFVBQVVYLFdBQVc7UUFBQ0E7S0FBUztJQUM1RSxNQUFNc0IsdUJBQXVCLENBQUMsR0FBRzlDLE9BQU93QyxXQUFXLEVBQUVMLFVBQVVWLGlCQUFpQjtRQUFDQTtLQUFlO0lBQ2hHLDZDQUE2QyxHQUU3QyxPQUFPYixPQUFPbUMsTUFBTSxDQUFDLENBQUMsR0FBR3BCLE9BQU87UUFDOUJLO0lBQ0YsR0FBR2IsV0FBVztRQUNaQSxTQUFTb0I7SUFDWCxHQUFHbkIsY0FBYztRQUNmQSxZQUFZcUI7SUFDZCxHQUFHcEIsYUFBYTtRQUNkQSxXQUFXcUI7SUFDYixHQUFHcEIsVUFBVTtRQUNYQSxRQUFRcUI7SUFDVixHQUFHcEIsYUFBYTtRQUNkQSxXQUFXcUI7SUFDYixHQUFHcEIsWUFBWTtRQUNiQSxVQUFVcUI7SUFDWixHQUFHcEIsa0JBQWtCO1FBQ25CQSxnQkFBZ0JxQjtJQUNsQixHQUFHO1FBQ0RwQixVQUFVLE9BQU9BLGFBQWEsYUFBYSxDQUFDc0IsUUFBUUMsYUFDcEQsMkRBQTJEO1lBQzNEdkIsU0FBU3NCLFFBQVFwQyxPQUFPbUMsTUFBTSxDQUFDLENBQUMsR0FBR0UsWUFBWTtnQkFDN0NsQixLQUFLRztZQUNQLE1BQW1CLFdBQUYsR0FBRyxJQUFHbEMsT0FBT2tELFlBQVksRUFBRXhCLFVBQVU7WUFDcERLLEtBQUtHO1FBQ1A7SUFDRjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L3VpL2Nqcy91c2VSVEdUcmFuc2l0aW9uUHJvcHMuanM/ZmNhYyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuZXhwb3J0cy5fX2VzTW9kdWxlID0gdHJ1ZTtcbmV4cG9ydHMuZGVmYXVsdCA9IHVzZVJUR1RyYW5zaXRpb25Qcm9wcztcbnZhciBfcmVhY3QgPSByZXF1aXJlKFwicmVhY3RcIik7XG52YXIgX3VzZU1lcmdlZFJlZnMgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCJAcmVzdGFydC9ob29rcy91c2VNZXJnZWRSZWZzXCIpKTtcbnZhciBfdXRpbHMgPSByZXF1aXJlKFwiLi91dGlsc1wiKTtcbmNvbnN0IF9leGNsdWRlZCA9IFtcIm9uRW50ZXJcIiwgXCJvbkVudGVyaW5nXCIsIFwib25FbnRlcmVkXCIsIFwib25FeGl0XCIsIFwib25FeGl0aW5nXCIsIFwib25FeGl0ZWRcIiwgXCJhZGRFbmRMaXN0ZW5lclwiLCBcImNoaWxkcmVuXCJdO1xuZnVuY3Rpb24gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChvYmopIHsgcmV0dXJuIG9iaiAmJiBvYmouX19lc01vZHVsZSA/IG9iaiA6IHsgZGVmYXVsdDogb2JqIH07IH1cbmZ1bmN0aW9uIF9vYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlKHNvdXJjZSwgZXhjbHVkZWQpIHsgaWYgKHNvdXJjZSA9PSBudWxsKSByZXR1cm4ge307IHZhciB0YXJnZXQgPSB7fTsgdmFyIHNvdXJjZUtleXMgPSBPYmplY3Qua2V5cyhzb3VyY2UpOyB2YXIga2V5LCBpOyBmb3IgKGkgPSAwOyBpIDwgc291cmNlS2V5cy5sZW5ndGg7IGkrKykgeyBrZXkgPSBzb3VyY2VLZXlzW2ldOyBpZiAoZXhjbHVkZWQuaW5kZXhPZihrZXkpID49IDApIGNvbnRpbnVlOyB0YXJnZXRba2V5XSA9IHNvdXJjZVtrZXldOyB9IHJldHVybiB0YXJnZXQ7IH1cbi8qKlxuICogTm9ybWFsaXplcyBSVEcgdHJhbnNpdGlvbiBjYWxsYmFja3Mgd2l0aCBub2RlUmVmIHRvIGJldHRlciBzdXBwb3J0XG4gKiBzdHJpY3QgbW9kZS5cbiAqXG4gKiBAcGFyYW0gcHJvcHMgVHJhbnNpdGlvbiBwcm9wcy5cbiAqIEByZXR1cm5zIE5vcm1hbGl6ZWQgdHJhbnNpdGlvbiBwcm9wcy5cbiAqL1xuZnVuY3Rpb24gdXNlUlRHVHJhbnNpdGlvblByb3BzKF9yZWYpIHtcbiAgbGV0IHtcbiAgICAgIG9uRW50ZXIsXG4gICAgICBvbkVudGVyaW5nLFxuICAgICAgb25FbnRlcmVkLFxuICAgICAgb25FeGl0LFxuICAgICAgb25FeGl0aW5nLFxuICAgICAgb25FeGl0ZWQsXG4gICAgICBhZGRFbmRMaXN0ZW5lcixcbiAgICAgIGNoaWxkcmVuXG4gICAgfSA9IF9yZWYsXG4gICAgcHJvcHMgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZShfcmVmLCBfZXhjbHVkZWQpO1xuICBjb25zdCB7XG4gICAgbWFqb3JcbiAgfSA9ICgwLCBfdXRpbHMuZ2V0UmVhY3RWZXJzaW9uKSgpO1xuICBjb25zdCBjaGlsZFJlZiA9IG1ham9yID49IDE5ID8gY2hpbGRyZW4ucHJvcHMucmVmIDogY2hpbGRyZW4ucmVmO1xuICBjb25zdCBub2RlUmVmID0gKDAsIF9yZWFjdC51c2VSZWYpKG51bGwpO1xuICBjb25zdCBtZXJnZWRSZWYgPSAoMCwgX3VzZU1lcmdlZFJlZnMuZGVmYXVsdCkobm9kZVJlZiwgdHlwZW9mIGNoaWxkcmVuID09PSAnZnVuY3Rpb24nID8gbnVsbCA6IGNoaWxkUmVmKTtcbiAgY29uc3Qgbm9ybWFsaXplID0gY2FsbGJhY2sgPT4gcGFyYW0gPT4ge1xuICAgIGlmIChjYWxsYmFjayAmJiBub2RlUmVmLmN1cnJlbnQpIHtcbiAgICAgIGNhbGxiYWNrKG5vZGVSZWYuY3VycmVudCwgcGFyYW0pO1xuICAgIH1cbiAgfTtcblxuICAvKiBlc2xpbnQtZGlzYWJsZSByZWFjdC1ob29rcy9leGhhdXN0aXZlLWRlcHMgKi9cbiAgY29uc3QgaGFuZGxlRW50ZXIgPSAoMCwgX3JlYWN0LnVzZUNhbGxiYWNrKShub3JtYWxpemUob25FbnRlciksIFtvbkVudGVyXSk7XG4gIGNvbnN0IGhhbmRsZUVudGVyaW5nID0gKDAsIF9yZWFjdC51c2VDYWxsYmFjaykobm9ybWFsaXplKG9uRW50ZXJpbmcpLCBbb25FbnRlcmluZ10pO1xuICBjb25zdCBoYW5kbGVFbnRlcmVkID0gKDAsIF9yZWFjdC51c2VDYWxsYmFjaykobm9ybWFsaXplKG9uRW50ZXJlZCksIFtvbkVudGVyZWRdKTtcbiAgY29uc3QgaGFuZGxlRXhpdCA9ICgwLCBfcmVhY3QudXNlQ2FsbGJhY2spKG5vcm1hbGl6ZShvbkV4aXQpLCBbb25FeGl0XSk7XG4gIGNvbnN0IGhhbmRsZUV4aXRpbmcgPSAoMCwgX3JlYWN0LnVzZUNhbGxiYWNrKShub3JtYWxpemUob25FeGl0aW5nKSwgW29uRXhpdGluZ10pO1xuICBjb25zdCBoYW5kbGVFeGl0ZWQgPSAoMCwgX3JlYWN0LnVzZUNhbGxiYWNrKShub3JtYWxpemUob25FeGl0ZWQpLCBbb25FeGl0ZWRdKTtcbiAgY29uc3QgaGFuZGxlQWRkRW5kTGlzdGVuZXIgPSAoMCwgX3JlYWN0LnVzZUNhbGxiYWNrKShub3JtYWxpemUoYWRkRW5kTGlzdGVuZXIpLCBbYWRkRW5kTGlzdGVuZXJdKTtcbiAgLyogZXNsaW50LWVuYWJsZSByZWFjdC1ob29rcy9leGhhdXN0aXZlLWRlcHMgKi9cblxuICByZXR1cm4gT2JqZWN0LmFzc2lnbih7fSwgcHJvcHMsIHtcbiAgICBub2RlUmVmXG4gIH0sIG9uRW50ZXIgJiYge1xuICAgIG9uRW50ZXI6IGhhbmRsZUVudGVyXG4gIH0sIG9uRW50ZXJpbmcgJiYge1xuICAgIG9uRW50ZXJpbmc6IGhhbmRsZUVudGVyaW5nXG4gIH0sIG9uRW50ZXJlZCAmJiB7XG4gICAgb25FbnRlcmVkOiBoYW5kbGVFbnRlcmVkXG4gIH0sIG9uRXhpdCAmJiB7XG4gICAgb25FeGl0OiBoYW5kbGVFeGl0XG4gIH0sIG9uRXhpdGluZyAmJiB7XG4gICAgb25FeGl0aW5nOiBoYW5kbGVFeGl0aW5nXG4gIH0sIG9uRXhpdGVkICYmIHtcbiAgICBvbkV4aXRlZDogaGFuZGxlRXhpdGVkXG4gIH0sIGFkZEVuZExpc3RlbmVyICYmIHtcbiAgICBhZGRFbmRMaXN0ZW5lcjogaGFuZGxlQWRkRW5kTGlzdGVuZXJcbiAgfSwge1xuICAgIGNoaWxkcmVuOiB0eXBlb2YgY2hpbGRyZW4gPT09ICdmdW5jdGlvbicgPyAoc3RhdHVzLCBpbm5lclByb3BzKSA9PlxuICAgIC8vIFRPRE86IFR5cGVzIGZvciBSVEcgbWlzc2luZyBpbm5lclByb3BzLCBzbyBuZWVkIHRvIGNhc3QuXG4gICAgY2hpbGRyZW4oc3RhdHVzLCBPYmplY3QuYXNzaWduKHt9LCBpbm5lclByb3BzLCB7XG4gICAgICByZWY6IG1lcmdlZFJlZlxuICAgIH0pKSA6IC8qI19fUFVSRV9fKi8oMCwgX3JlYWN0LmNsb25lRWxlbWVudCkoY2hpbGRyZW4sIHtcbiAgICAgIHJlZjogbWVyZ2VkUmVmXG4gICAgfSlcbiAgfSk7XG59Il0sIm5hbWVzIjpbImV4cG9ydHMiLCJfX2VzTW9kdWxlIiwiZGVmYXVsdCIsInVzZVJUR1RyYW5zaXRpb25Qcm9wcyIsIl9yZWFjdCIsInJlcXVpcmUiLCJfdXNlTWVyZ2VkUmVmcyIsIl9pbnRlcm9wUmVxdWlyZURlZmF1bHQiLCJfdXRpbHMiLCJfZXhjbHVkZWQiLCJvYmoiLCJfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZSIsInNvdXJjZSIsImV4Y2x1ZGVkIiwidGFyZ2V0Iiwic291cmNlS2V5cyIsIk9iamVjdCIsImtleXMiLCJrZXkiLCJpIiwibGVuZ3RoIiwiaW5kZXhPZiIsIl9yZWYiLCJvbkVudGVyIiwib25FbnRlcmluZyIsIm9uRW50ZXJlZCIsIm9uRXhpdCIsIm9uRXhpdGluZyIsIm9uRXhpdGVkIiwiYWRkRW5kTGlzdGVuZXIiLCJjaGlsZHJlbiIsInByb3BzIiwibWFqb3IiLCJnZXRSZWFjdFZlcnNpb24iLCJjaGlsZFJlZiIsInJlZiIsIm5vZGVSZWYiLCJ1c2VSZWYiLCJtZXJnZWRSZWYiLCJub3JtYWxpemUiLCJjYWxsYmFjayIsInBhcmFtIiwiY3VycmVudCIsImhhbmRsZUVudGVyIiwidXNlQ2FsbGJhY2siLCJoYW5kbGVFbnRlcmluZyIsImhhbmRsZUVudGVyZWQiLCJoYW5kbGVFeGl0IiwiaGFuZGxlRXhpdGluZyIsImhhbmRsZUV4aXRlZCIsImhhbmRsZUFkZEVuZExpc3RlbmVyIiwiYXNzaWduIiwic3RhdHVzIiwiaW5uZXJQcm9wcyIsImNsb25lRWxlbWVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/useRTGTransitionProps.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/useWaitForDOMRef.js":
/*!**************************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/useWaitForDOMRef.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = useWaitForDOMRef;\nexports.resolveContainerRef = void 0;\nvar _ownerDocument = _interopRequireDefault(__webpack_require__(/*! dom-helpers/ownerDocument */ \"(ssr)/../../node_modules/dom-helpers/esm/ownerDocument.js\"));\nvar _canUseDOM = _interopRequireDefault(__webpack_require__(/*! dom-helpers/canUseDOM */ \"(ssr)/../../node_modules/dom-helpers/esm/canUseDOM.js\"));\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar _useWindow = _interopRequireDefault(__webpack_require__(/*! ./useWindow */ \"(ssr)/../../node_modules/@restart/ui/cjs/useWindow.js\"));\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nconst resolveContainerRef = (ref, document)=>{\n    if (!_canUseDOM.default) return null;\n    if (ref == null) return (document || (0, _ownerDocument.default)()).body;\n    if (typeof ref === \"function\") ref = ref();\n    if (ref && \"current\" in ref) ref = ref.current;\n    if (ref && (\"nodeType\" in ref || ref.getBoundingClientRect)) return ref;\n    return null;\n};\nexports.resolveContainerRef = resolveContainerRef;\nfunction useWaitForDOMRef(ref, onResolved) {\n    const window = (0, _useWindow.default)();\n    const [resolvedRef, setRef] = (0, _react.useState)(()=>resolveContainerRef(ref, window == null ? void 0 : window.document));\n    if (!resolvedRef) {\n        const earlyRef = resolveContainerRef(ref);\n        if (earlyRef) setRef(earlyRef);\n    }\n    (0, _react.useEffect)(()=>{\n        if (onResolved && resolvedRef) {\n            onResolved(resolvedRef);\n        }\n    }, [\n        onResolved,\n        resolvedRef\n    ]);\n    (0, _react.useEffect)(()=>{\n        const nextRef = resolveContainerRef(ref);\n        if (nextRef !== resolvedRef) {\n            setRef(nextRef);\n        }\n    }, [\n        ref,\n        resolvedRef\n    ]);\n    return resolvedRef;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/useWaitForDOMRef.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/useWindow.js":
/*!*******************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/useWindow.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = useWindow;\nexports.WindowProvider = void 0;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar _canUseDOM = _interopRequireDefault(__webpack_require__(/*! dom-helpers/canUseDOM */ \"(ssr)/../../node_modules/dom-helpers/esm/canUseDOM.js\"));\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nconst Context = /*#__PURE__*/ (0, _react.createContext)(_canUseDOM.default ? window : undefined);\nconst WindowProvider = Context.Provider;\n/**\n * The document \"window\" placed in React context. Helpful for determining\n * SSR context, or when rendering into an iframe.\n *\n * @returns the current window\n */ exports.WindowProvider = WindowProvider;\nfunction useWindow() {\n    return (0, _react.useContext)(Context);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/useWindow.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/cjs/utils.js":
/*!***************************************************!*\
  !*** ../../node_modules/@restart/ui/cjs/utils.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports.isEscKey = isEscKey;\nexports.getReactVersion = getReactVersion;\nvar React = _interopRequireWildcard(__webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nfunction isEscKey(e) {\n    return e.code === \"Escape\" || e.keyCode === 27;\n}\nfunction getReactVersion() {\n    const parts = React.version.split(\".\");\n    return {\n        major: +parts[0],\n        minor: +parts[1],\n        patch: +parts[2]\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/cjs/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/index.js":
/*!*******************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/index.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nvar _useCallbackRef = _interopRequireDefault(__webpack_require__(/*! ./useCallbackRef */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useCallbackRef.js\"));\nexports.useCallbackRef = _useCallbackRef.default;\nvar _useCommittedRef = _interopRequireDefault(__webpack_require__(/*! ./useCommittedRef */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useCommittedRef.js\"));\nexports.useCommittedRef = _useCommittedRef.default;\nvar _useEventCallback = _interopRequireDefault(__webpack_require__(/*! ./useEventCallback */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventCallback.js\"));\nexports.useEventCallback = _useEventCallback.default;\nvar _useEventListener = _interopRequireDefault(__webpack_require__(/*! ./useEventListener */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventListener.js\"));\nexports.useEventListener = _useEventListener.default;\nvar _useGlobalListener = _interopRequireDefault(__webpack_require__(/*! ./useGlobalListener */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useGlobalListener.js\"));\nexports.useGlobalListener = _useGlobalListener.default;\nvar _useInterval = _interopRequireDefault(__webpack_require__(/*! ./useInterval */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useInterval.js\"));\nexports.useInterval = _useInterval.default;\nvar _useRafInterval = _interopRequireDefault(__webpack_require__(/*! ./useRafInterval */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useRafInterval.js\"));\nexports.useRafInterval = _useRafInterval.default;\nvar _useMergeState = _interopRequireDefault(__webpack_require__(/*! ./useMergeState */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergeState.js\"));\nexports.useMergeState = _useMergeState.default;\nvar _useMergeStateFromProps = _interopRequireDefault(__webpack_require__(/*! ./useMergeStateFromProps */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergeStateFromProps.js\"));\nexports.useMergeStateFromProps = _useMergeStateFromProps.default;\nvar _useMounted = _interopRequireDefault(__webpack_require__(/*! ./useMounted */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMounted.js\"));\nexports.useMounted = _useMounted.default;\nvar _usePrevious = _interopRequireDefault(__webpack_require__(/*! ./usePrevious */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/usePrevious.js\"));\nexports.usePrevious = _usePrevious.default;\nvar _useImage = _interopRequireDefault(__webpack_require__(/*! ./useImage */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useImage.js\"));\nexports.useImage = _useImage.default;\nvar _useResizeObserver = _interopRequireDefault(__webpack_require__(/*! ./useResizeObserver */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useResizeObserver.js\"));\nexports.useResizeObserver = _useResizeObserver.default;\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useCallbackRef.js":
/*!****************************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useCallbackRef.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = useCallbackRef;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/**\n * A convenience hook around `useState` designed to be paired with\n * the component [callback ref](https://reactjs.org/docs/refs-and-the-dom.html#callback-refs) api.\n * Callback refs are useful over `useRef()` when you need to respond to the ref being set\n * instead of lazily accessing it in an effect.\n *\n * ```ts\n * const [element, attachRef] = useCallbackRef<HTMLDivElement>()\n *\n * useEffect(() => {\n *   if (!element) return\n *\n *   const calendar = new FullCalendar.Calendar(element)\n *\n *   return () => {\n *     calendar.destroy()\n *   }\n * }, [element])\n *\n * return <div ref={attachRef} />\n * ```\n *\n * @category refs\n */ function useCallbackRef() {\n    return (0, _react.useState)(null);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L3VpL25vZGVfbW9kdWxlcy9AcmVzdGFydC9ob29rcy9janMvdXNlQ2FsbGJhY2tSZWYuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFFYkEsa0JBQWtCLEdBQUc7QUFDckJBLGtCQUFlLEdBQUdHO0FBQ2xCLElBQUlDLFNBQVNDLG1CQUFPQSxDQUFDLDRHQUFPO0FBQzVCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQXVCQyxHQUNELFNBQVNGO0lBQ1AsT0FBTyxDQUFDLEdBQUdDLE9BQU9FLFFBQVEsRUFBRTtBQUM5QiIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9AcmVzdGFydC91aS9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvY2pzL3VzZUNhbGxiYWNrUmVmLmpzP2Y3MzIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbmV4cG9ydHMuX19lc01vZHVsZSA9IHRydWU7XG5leHBvcnRzLmRlZmF1bHQgPSB1c2VDYWxsYmFja1JlZjtcbnZhciBfcmVhY3QgPSByZXF1aXJlKFwicmVhY3RcIik7XG4vKipcbiAqIEEgY29udmVuaWVuY2UgaG9vayBhcm91bmQgYHVzZVN0YXRlYCBkZXNpZ25lZCB0byBiZSBwYWlyZWQgd2l0aFxuICogdGhlIGNvbXBvbmVudCBbY2FsbGJhY2sgcmVmXShodHRwczovL3JlYWN0anMub3JnL2RvY3MvcmVmcy1hbmQtdGhlLWRvbS5odG1sI2NhbGxiYWNrLXJlZnMpIGFwaS5cbiAqIENhbGxiYWNrIHJlZnMgYXJlIHVzZWZ1bCBvdmVyIGB1c2VSZWYoKWAgd2hlbiB5b3UgbmVlZCB0byByZXNwb25kIHRvIHRoZSByZWYgYmVpbmcgc2V0XG4gKiBpbnN0ZWFkIG9mIGxhemlseSBhY2Nlc3NpbmcgaXQgaW4gYW4gZWZmZWN0LlxuICpcbiAqIGBgYHRzXG4gKiBjb25zdCBbZWxlbWVudCwgYXR0YWNoUmVmXSA9IHVzZUNhbGxiYWNrUmVmPEhUTUxEaXZFbGVtZW50PigpXG4gKlxuICogdXNlRWZmZWN0KCgpID0+IHtcbiAqICAgaWYgKCFlbGVtZW50KSByZXR1cm5cbiAqXG4gKiAgIGNvbnN0IGNhbGVuZGFyID0gbmV3IEZ1bGxDYWxlbmRhci5DYWxlbmRhcihlbGVtZW50KVxuICpcbiAqICAgcmV0dXJuICgpID0+IHtcbiAqICAgICBjYWxlbmRhci5kZXN0cm95KClcbiAqICAgfVxuICogfSwgW2VsZW1lbnRdKVxuICpcbiAqIHJldHVybiA8ZGl2IHJlZj17YXR0YWNoUmVmfSAvPlxuICogYGBgXG4gKlxuICogQGNhdGVnb3J5IHJlZnNcbiAqL1xuZnVuY3Rpb24gdXNlQ2FsbGJhY2tSZWYoKSB7XG4gIHJldHVybiAoMCwgX3JlYWN0LnVzZVN0YXRlKShudWxsKTtcbn0iXSwibmFtZXMiOlsiZXhwb3J0cyIsIl9fZXNNb2R1bGUiLCJkZWZhdWx0IiwidXNlQ2FsbGJhY2tSZWYiLCJfcmVhY3QiLCJyZXF1aXJlIiwidXNlU3RhdGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useCallbackRef.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useCommittedRef.js":
/*!*****************************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useCommittedRef.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/**\n * Creates a `Ref` whose value is updated in an effect, ensuring the most recent\n * value is the one rendered with. Generally only required for Concurrent mode usage\n * where previous work in `render()` may be discarded before being used.\n *\n * This is safe to access in an event handler.\n *\n * @param value The `Ref` value\n */ function useCommittedRef(value) {\n    const ref = (0, _react.useRef)(value);\n    (0, _react.useEffect)(()=>{\n        ref.current = value;\n    }, [\n        value\n    ]);\n    return ref;\n}\nvar _default = useCommittedRef;\nexports[\"default\"] = _default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L3VpL25vZGVfbW9kdWxlcy9AcmVzdGFydC9ob29rcy9janMvdXNlQ29tbWl0dGVkUmVmLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBRWJBLGtCQUFrQixHQUFHO0FBQ3JCQSxrQkFBZSxHQUFHLEtBQUs7QUFDdkIsSUFBSUcsU0FBU0MsbUJBQU9BLENBQUMsNEdBQU87QUFDNUI7Ozs7Ozs7O0NBUUMsR0FDRCxTQUFTQyxnQkFBZ0JDLEtBQUs7SUFDNUIsTUFBTUMsTUFBTSxDQUFDLEdBQUdKLE9BQU9LLE1BQU0sRUFBRUY7SUFDOUIsSUFBR0gsT0FBT00sU0FBUyxFQUFFO1FBQ3BCRixJQUFJRyxPQUFPLEdBQUdKO0lBQ2hCLEdBQUc7UUFBQ0E7S0FBTTtJQUNWLE9BQU9DO0FBQ1Q7QUFDQSxJQUFJSSxXQUFXTjtBQUNmTCxrQkFBZSxHQUFHVyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9AcmVzdGFydC91aS9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvY2pzL3VzZUNvbW1pdHRlZFJlZi5qcz9hNjhhIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5leHBvcnRzLl9fZXNNb2R1bGUgPSB0cnVlO1xuZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwO1xudmFyIF9yZWFjdCA9IHJlcXVpcmUoXCJyZWFjdFwiKTtcbi8qKlxuICogQ3JlYXRlcyBhIGBSZWZgIHdob3NlIHZhbHVlIGlzIHVwZGF0ZWQgaW4gYW4gZWZmZWN0LCBlbnN1cmluZyB0aGUgbW9zdCByZWNlbnRcbiAqIHZhbHVlIGlzIHRoZSBvbmUgcmVuZGVyZWQgd2l0aC4gR2VuZXJhbGx5IG9ubHkgcmVxdWlyZWQgZm9yIENvbmN1cnJlbnQgbW9kZSB1c2FnZVxuICogd2hlcmUgcHJldmlvdXMgd29yayBpbiBgcmVuZGVyKClgIG1heSBiZSBkaXNjYXJkZWQgYmVmb3JlIGJlaW5nIHVzZWQuXG4gKlxuICogVGhpcyBpcyBzYWZlIHRvIGFjY2VzcyBpbiBhbiBldmVudCBoYW5kbGVyLlxuICpcbiAqIEBwYXJhbSB2YWx1ZSBUaGUgYFJlZmAgdmFsdWVcbiAqL1xuZnVuY3Rpb24gdXNlQ29tbWl0dGVkUmVmKHZhbHVlKSB7XG4gIGNvbnN0IHJlZiA9ICgwLCBfcmVhY3QudXNlUmVmKSh2YWx1ZSk7XG4gICgwLCBfcmVhY3QudXNlRWZmZWN0KSgoKSA9PiB7XG4gICAgcmVmLmN1cnJlbnQgPSB2YWx1ZTtcbiAgfSwgW3ZhbHVlXSk7XG4gIHJldHVybiByZWY7XG59XG52YXIgX2RlZmF1bHQgPSB1c2VDb21taXR0ZWRSZWY7XG5leHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDsiXSwibmFtZXMiOlsiZXhwb3J0cyIsIl9fZXNNb2R1bGUiLCJkZWZhdWx0IiwiX3JlYWN0IiwicmVxdWlyZSIsInVzZUNvbW1pdHRlZFJlZiIsInZhbHVlIiwicmVmIiwidXNlUmVmIiwidXNlRWZmZWN0IiwiY3VycmVudCIsIl9kZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useCommittedRef.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventCallback.js":
/*!******************************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventCallback.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = useEventCallback;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar _useCommittedRef = _interopRequireDefault(__webpack_require__(/*! ./useCommittedRef */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useCommittedRef.js\"));\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction useEventCallback(fn) {\n    const ref = (0, _useCommittedRef.default)(fn);\n    return (0, _react.useCallback)(function(...args) {\n        return ref.current && ref.current(...args);\n    }, [\n        ref\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L3VpL25vZGVfbW9kdWxlcy9AcmVzdGFydC9ob29rcy9janMvdXNlRXZlbnRDYWxsYmFjay5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUViQSxrQkFBa0IsR0FBRztBQUNyQkEsa0JBQWUsR0FBR0c7QUFDbEIsSUFBSUMsU0FBU0MsbUJBQU9BLENBQUMsNEdBQU87QUFDNUIsSUFBSUMsbUJBQW1CQyx1QkFBdUJGLG1CQUFPQSxDQUFDLGtIQUFtQjtBQUN6RSxTQUFTRSx1QkFBdUJDLEdBQUc7SUFBSSxPQUFPQSxPQUFPQSxJQUFJUCxVQUFVLEdBQUdPLE1BQU07UUFBRU4sU0FBU007SUFBSTtBQUFHO0FBQzlGLFNBQVNMLGlCQUFpQk0sRUFBRTtJQUMxQixNQUFNQyxNQUFNLENBQUMsR0FBR0osaUJBQWlCSixPQUFPLEVBQUVPO0lBQzFDLE9BQU8sQ0FBQyxHQUFHTCxPQUFPTyxXQUFXLEVBQUUsU0FBVSxHQUFHQyxJQUFJO1FBQzlDLE9BQU9GLElBQUlHLE9BQU8sSUFBSUgsSUFBSUcsT0FBTyxJQUFJRDtJQUN2QyxHQUFHO1FBQUNGO0tBQUk7QUFDViIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4uLy4uL25vZGVfbW9kdWxlcy9AcmVzdGFydC91aS9ub2RlX21vZHVsZXMvQHJlc3RhcnQvaG9va3MvY2pzL3VzZUV2ZW50Q2FsbGJhY2suanM/MmI5NiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuZXhwb3J0cy5fX2VzTW9kdWxlID0gdHJ1ZTtcbmV4cG9ydHMuZGVmYXVsdCA9IHVzZUV2ZW50Q2FsbGJhY2s7XG52YXIgX3JlYWN0ID0gcmVxdWlyZShcInJlYWN0XCIpO1xudmFyIF91c2VDb21taXR0ZWRSZWYgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuL3VzZUNvbW1pdHRlZFJlZlwiKSk7XG5mdW5jdGlvbiBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KG9iaikgeyByZXR1cm4gb2JqICYmIG9iai5fX2VzTW9kdWxlID8gb2JqIDogeyBkZWZhdWx0OiBvYmogfTsgfVxuZnVuY3Rpb24gdXNlRXZlbnRDYWxsYmFjayhmbikge1xuICBjb25zdCByZWYgPSAoMCwgX3VzZUNvbW1pdHRlZFJlZi5kZWZhdWx0KShmbik7XG4gIHJldHVybiAoMCwgX3JlYWN0LnVzZUNhbGxiYWNrKShmdW5jdGlvbiAoLi4uYXJncykge1xuICAgIHJldHVybiByZWYuY3VycmVudCAmJiByZWYuY3VycmVudCguLi5hcmdzKTtcbiAgfSwgW3JlZl0pO1xufSJdLCJuYW1lcyI6WyJleHBvcnRzIiwiX19lc01vZHVsZSIsImRlZmF1bHQiLCJ1c2VFdmVudENhbGxiYWNrIiwiX3JlYWN0IiwicmVxdWlyZSIsIl91c2VDb21taXR0ZWRSZWYiLCJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iiwib2JqIiwiZm4iLCJyZWYiLCJ1c2VDYWxsYmFjayIsImFyZ3MiLCJjdXJyZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventCallback.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventListener.js":
/*!******************************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventListener.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = useEventListener;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar _useEventCallback = _interopRequireDefault(__webpack_require__(/*! ./useEventCallback */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventCallback.js\"));\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\n/**\n * Attaches an event handler outside directly to specified DOM element\n * bypassing the react synthetic event system.\n *\n * @param element The target to listen for events on\n * @param event The DOM event name\n * @param handler An event handler\n * @param capture Whether or not to listen during the capture event phase\n */ function useEventListener(eventTarget, event, listener, capture = false) {\n    const handler = (0, _useEventCallback.default)(listener);\n    (0, _react.useEffect)(()=>{\n        const target = typeof eventTarget === \"function\" ? eventTarget() : eventTarget;\n        target.addEventListener(event, handler, capture);\n        return ()=>target.removeEventListener(event, handler, capture);\n    }, [\n        eventTarget\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventListener.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useForceUpdate.js":
/*!****************************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useForceUpdate.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = useForceUpdate;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/**\n * Returns a function that triggers a component update. the hook equivalent to\n * `this.forceUpdate()` in a class component. In most cases using a state value directly\n * is preferable but may be required in some advanced usages of refs for interop or\n * when direct DOM manipulation is required.\n *\n * ```ts\n * const forceUpdate = useForceUpdate();\n *\n * const updateOnClick = useCallback(() => {\n *  forceUpdate()\n * }, [forceUpdate])\n *\n * return <button type=\"button\" onClick={updateOnClick}>Hi there</button>\n * ```\n */ function useForceUpdate() {\n    // The toggling state value is designed to defeat React optimizations for skipping\n    // updates when they are strictly equal to the last state value\n    const [, dispatch] = (0, _react.useReducer)((revision)=>revision + 1, 0);\n    return dispatch;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useForceUpdate.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useGlobalListener.js":
/*!*******************************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useGlobalListener.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = useGlobalListener;\nvar _useEventListener = _interopRequireDefault(__webpack_require__(/*! ./useEventListener */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useEventListener.js\"));\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\n/**\n * Attaches an event handler outside directly to the `document`,\n * bypassing the react synthetic event system.\n *\n * ```ts\n * useGlobalListener('keydown', (event) => {\n *  console.log(event.key)\n * })\n * ```\n *\n * @param event The DOM event name\n * @param handler An event handler\n * @param capture Whether or not to listen during the capture event phase\n */ function useGlobalListener(event, handler, capture = false) {\n    const documentTarget = (0, _react.useCallback)(()=>document, []);\n    return (0, _useEventListener.default)(documentTarget, event, handler, capture);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useGlobalListener.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useImage.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useImage.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = useImage;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/**\n * Fetch and load an image for programatic use such as in a `<canvas>` element.\n *\n * @param imageOrUrl The `HtmlImageElement` or image url to load\n * @param crossOrigin The `crossorigin` attribute to set\n *\n * ```ts\n * const { image, error } = useImage('/static/kittens.png')\n * const ref = useRef<HTMLCanvasElement>()\n *\n * useEffect(() => {\n *   const ctx = ref.current.getContext('2d')\n *\n *   if (image) {\n *     ctx.drawImage(image, 0, 0)\n *   }\n * }, [ref, image])\n *\n * return (\n *   <>\n *     {error && \"there was a problem loading the image\"}\n *     <canvas ref={ref} />\n *   </>\n * ```\n */ function useImage(imageOrUrl, crossOrigin) {\n    const [state, setState] = (0, _react.useState)({\n        image: null,\n        error: null\n    });\n    (0, _react.useEffect)(()=>{\n        if (!imageOrUrl) return undefined;\n        let image;\n        if (typeof imageOrUrl === \"string\") {\n            image = new Image();\n            if (crossOrigin) image.crossOrigin = crossOrigin;\n            image.src = imageOrUrl;\n        } else {\n            image = imageOrUrl;\n            if (image.complete && image.naturalHeight > 0) {\n                setState({\n                    image,\n                    error: null\n                });\n                return;\n            }\n        }\n        function onLoad() {\n            setState({\n                image,\n                error: null\n            });\n        }\n        function onError(error) {\n            setState({\n                image,\n                error\n            });\n        }\n        image.addEventListener(\"load\", onLoad);\n        image.addEventListener(\"error\", onError);\n        return ()=>{\n            image.removeEventListener(\"load\", onLoad);\n            image.removeEventListener(\"error\", onError);\n        };\n    }, [\n        imageOrUrl,\n        crossOrigin\n    ]);\n    return state;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useImage.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useInterval.js":
/*!*************************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useInterval.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar _useCommittedRef = _interopRequireDefault(__webpack_require__(/*! ./useCommittedRef */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useCommittedRef.js\"));\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\n/**\n * Creates a `setInterval` that is properly cleaned up when a component unmounted\n *\n * ```tsx\n *  function Timer() {\n *    const [timer, setTimer] = useState(0)\n *    useInterval(() => setTimer(i => i + 1), 1000)\n *\n *    return <span>{timer} seconds past</span>\n *  }\n * ```\n *\n * @param fn an function run on each interval\n * @param ms The milliseconds duration of the interval\n */ /**\n * Creates a pausable `setInterval` that is properly cleaned up when a component unmounted\n *\n * ```tsx\n *  const [paused, setPaused] = useState(false)\n *  const [timer, setTimer] = useState(0)\n *\n *  useInterval(() => setTimer(i => i + 1), 1000, paused)\n *\n *  return (\n *    <span>\n *      {timer} seconds past\n *\n *      <button onClick={() => setPaused(p => !p)}>{paused ? 'Play' : 'Pause' }</button>\n *    </span>\n * )\n * ```\n *\n * @param fn an function run on each interval\n * @param ms The milliseconds duration of the interval\n * @param paused Whether or not the interval is currently running\n */ /**\n * Creates a pausable `setInterval` that _fires_ immediately and is\n * properly cleaned up when a component unmounted\n *\n * ```tsx\n *  const [timer, setTimer] = useState(-1)\n *  useInterval(() => setTimer(i => i + 1), 1000, false, true)\n *\n *  // will update to 0 on the first effect\n *  return <span>{timer} seconds past</span>\n * ```\n *\n * @param fn an function run on each interval\n * @param ms The milliseconds duration of the interval\n * @param paused Whether or not the interval is currently running\n * @param runImmediately Whether to run the function immediately on mount or unpause\n * rather than waiting for the first interval to elapse\n *\n\n */ function useInterval(fn, ms, paused = false, runImmediately = false) {\n    let handle;\n    const fnRef = (0, _useCommittedRef.default)(fn);\n    // this ref is necessary b/c useEffect will sometimes miss a paused toggle\n    // orphaning a setTimeout chain in the aether, so relying on it's refresh logic is not reliable.\n    const pausedRef = (0, _useCommittedRef.default)(paused);\n    const tick = ()=>{\n        if (pausedRef.current) return;\n        fnRef.current();\n        schedule(); // eslint-disable-line no-use-before-define\n    };\n    const schedule = ()=>{\n        clearTimeout(handle);\n        handle = setTimeout(tick, ms);\n    };\n    (0, _react.useEffect)(()=>{\n        if (runImmediately) {\n            tick();\n        } else {\n            schedule();\n        }\n        return ()=>clearTimeout(handle);\n    }, [\n        paused,\n        runImmediately\n    ]);\n}\nvar _default = useInterval;\nexports[\"default\"] = _default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useInterval.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useIsomorphicEffect.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useIsomorphicEffect.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nconst isReactNative = typeof global !== \"undefined\" && // @ts-ignore\nglobal.navigator && // @ts-ignore\nglobal.navigator.product === \"ReactNative\";\nconst isDOM = typeof document !== \"undefined\";\n/**\n * Is `useLayoutEffect` in a DOM or React Native environment, otherwise resolves to useEffect\n * Only useful to avoid the console warning.\n *\n * PREFER `useEffect` UNLESS YOU KNOW WHAT YOU ARE DOING.\n *\n * @category effects\n */ var _default = isDOM || isReactNative ? _react.useLayoutEffect : _react.useEffect;\nexports[\"default\"] = _default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L3VpL25vZGVfbW9kdWxlcy9AcmVzdGFydC9ob29rcy9janMvdXNlSXNvbW9ycGhpY0VmZmVjdC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUViQSxrQkFBa0IsR0FBRztBQUNyQkEsa0JBQWUsR0FBRyxLQUFLO0FBQ3ZCLElBQUlHLFNBQVNDLG1CQUFPQSxDQUFDLDRHQUFPO0FBQzVCLE1BQU1DLGdCQUFnQixPQUFPQyxXQUFXLGVBQ3hDLGFBQWE7QUFDYkEsT0FBT0MsU0FBUyxJQUNoQixhQUFhO0FBQ2JELE9BQU9DLFNBQVMsQ0FBQ0MsT0FBTyxLQUFLO0FBQzdCLE1BQU1DLFFBQVEsT0FBT0MsYUFBYTtBQUVsQzs7Ozs7OztDQU9DLEdBQ0QsSUFBSUMsV0FBV0YsU0FBU0osZ0JBQWdCRixPQUFPUyxlQUFlLEdBQUdULE9BQU9VLFNBQVM7QUFDakZiLGtCQUFlLEdBQUdXIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L3VpL25vZGVfbW9kdWxlcy9AcmVzdGFydC9ob29rcy9janMvdXNlSXNvbW9ycGhpY0VmZmVjdC5qcz9mMmY5Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5leHBvcnRzLl9fZXNNb2R1bGUgPSB0cnVlO1xuZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwO1xudmFyIF9yZWFjdCA9IHJlcXVpcmUoXCJyZWFjdFwiKTtcbmNvbnN0IGlzUmVhY3ROYXRpdmUgPSB0eXBlb2YgZ2xvYmFsICE9PSAndW5kZWZpbmVkJyAmJlxuLy8gQHRzLWlnbm9yZVxuZ2xvYmFsLm5hdmlnYXRvciAmJlxuLy8gQHRzLWlnbm9yZVxuZ2xvYmFsLm5hdmlnYXRvci5wcm9kdWN0ID09PSAnUmVhY3ROYXRpdmUnO1xuY29uc3QgaXNET00gPSB0eXBlb2YgZG9jdW1lbnQgIT09ICd1bmRlZmluZWQnO1xuXG4vKipcbiAqIElzIGB1c2VMYXlvdXRFZmZlY3RgIGluIGEgRE9NIG9yIFJlYWN0IE5hdGl2ZSBlbnZpcm9ubWVudCwgb3RoZXJ3aXNlIHJlc29sdmVzIHRvIHVzZUVmZmVjdFxuICogT25seSB1c2VmdWwgdG8gYXZvaWQgdGhlIGNvbnNvbGUgd2FybmluZy5cbiAqXG4gKiBQUkVGRVIgYHVzZUVmZmVjdGAgVU5MRVNTIFlPVSBLTk9XIFdIQVQgWU9VIEFSRSBET0lORy5cbiAqXG4gKiBAY2F0ZWdvcnkgZWZmZWN0c1xuICovXG52YXIgX2RlZmF1bHQgPSBpc0RPTSB8fCBpc1JlYWN0TmF0aXZlID8gX3JlYWN0LnVzZUxheW91dEVmZmVjdCA6IF9yZWFjdC51c2VFZmZlY3Q7XG5leHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDsiXSwibmFtZXMiOlsiZXhwb3J0cyIsIl9fZXNNb2R1bGUiLCJkZWZhdWx0IiwiX3JlYWN0IiwicmVxdWlyZSIsImlzUmVhY3ROYXRpdmUiLCJnbG9iYWwiLCJuYXZpZ2F0b3IiLCJwcm9kdWN0IiwiaXNET00iLCJkb2N1bWVudCIsIl9kZWZhdWx0IiwidXNlTGF5b3V0RWZmZWN0IiwidXNlRWZmZWN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useIsomorphicEffect.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergeState.js":
/*!***************************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergeState.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = useMergeState;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/**\n * Updates state, partial updates are merged into existing state values\n */ /**\n * Mimics a React class component's state model, of having a single unified\n * `state` object and an updater that merges updates into the existing state, as\n * opposed to replacing it.\n *\n * ```js\n * const [state, setState] = useMergeState({ name: 'Betsy', age: 24 })\n *\n * setState({ name: 'Johan' }) // { name: 'Johan', age: 24 }\n *\n * setState(state => ({ age: state.age + 10 })) // { name: 'Johan', age: 34 }\n * ```\n *\n * @param initialState The initial state object\n */ function useMergeState(initialState) {\n    const [state, setState] = (0, _react.useState)(initialState);\n    const updater = (0, _react.useCallback)((update)=>{\n        if (update === null) return;\n        if (typeof update === \"function\") {\n            setState((state)=>{\n                const nextState = update(state);\n                return nextState == null ? state : Object.assign({}, state, nextState);\n            });\n        } else {\n            setState((state)=>Object.assign({}, state, update));\n        }\n    }, [\n        setState\n    ]);\n    return [\n        state,\n        updater\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergeState.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergeStateFromProps.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergeStateFromProps.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = useMergeStateFromProps;\nvar _useMergeState = _interopRequireDefault(__webpack_require__(/*! ./useMergeState */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergeState.js\"));\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction useMergeStateFromProps(props, gDSFP, initialState) {\n    const [state, setState] = (0, _useMergeState.default)(initialState);\n    const nextState = gDSFP(props, state);\n    if (nextState !== null) setState(nextState);\n    return [\n        state,\n        setState\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L3VpL25vZGVfbW9kdWxlcy9AcmVzdGFydC9ob29rcy9janMvdXNlTWVyZ2VTdGF0ZUZyb21Qcm9wcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUViQSxrQkFBa0IsR0FBRztBQUNyQkEsa0JBQWUsR0FBR0c7QUFDbEIsSUFBSUMsaUJBQWlCQyx1QkFBdUJDLG1CQUFPQSxDQUFDLDhHQUFpQjtBQUNyRSxTQUFTRCx1QkFBdUJFLEdBQUc7SUFBSSxPQUFPQSxPQUFPQSxJQUFJTixVQUFVLEdBQUdNLE1BQU07UUFBRUwsU0FBU0s7SUFBSTtBQUFHO0FBQzlGLFNBQVNKLHVCQUF1QkssS0FBSyxFQUFFQyxLQUFLLEVBQUVDLFlBQVk7SUFDeEQsTUFBTSxDQUFDQyxPQUFPQyxTQUFTLEdBQUcsQ0FBQyxHQUFHUixlQUFlRixPQUFPLEVBQUVRO0lBQ3RELE1BQU1HLFlBQVlKLE1BQU1ELE9BQU9HO0lBQy9CLElBQUlFLGNBQWMsTUFBTUQsU0FBU0M7SUFDakMsT0FBTztRQUFDRjtRQUFPQztLQUFTO0FBQzFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L3VpL25vZGVfbW9kdWxlcy9AcmVzdGFydC9ob29rcy9janMvdXNlTWVyZ2VTdGF0ZUZyb21Qcm9wcy5qcz9hN2RiIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5leHBvcnRzLl9fZXNNb2R1bGUgPSB0cnVlO1xuZXhwb3J0cy5kZWZhdWx0ID0gdXNlTWVyZ2VTdGF0ZUZyb21Qcm9wcztcbnZhciBfdXNlTWVyZ2VTdGF0ZSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4vdXNlTWVyZ2VTdGF0ZVwiKSk7XG5mdW5jdGlvbiBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KG9iaikgeyByZXR1cm4gb2JqICYmIG9iai5fX2VzTW9kdWxlID8gb2JqIDogeyBkZWZhdWx0OiBvYmogfTsgfVxuZnVuY3Rpb24gdXNlTWVyZ2VTdGF0ZUZyb21Qcm9wcyhwcm9wcywgZ0RTRlAsIGluaXRpYWxTdGF0ZSkge1xuICBjb25zdCBbc3RhdGUsIHNldFN0YXRlXSA9ICgwLCBfdXNlTWVyZ2VTdGF0ZS5kZWZhdWx0KShpbml0aWFsU3RhdGUpO1xuICBjb25zdCBuZXh0U3RhdGUgPSBnRFNGUChwcm9wcywgc3RhdGUpO1xuICBpZiAobmV4dFN0YXRlICE9PSBudWxsKSBzZXRTdGF0ZShuZXh0U3RhdGUpO1xuICByZXR1cm4gW3N0YXRlLCBzZXRTdGF0ZV07XG59Il0sIm5hbWVzIjpbImV4cG9ydHMiLCJfX2VzTW9kdWxlIiwiZGVmYXVsdCIsInVzZU1lcmdlU3RhdGVGcm9tUHJvcHMiLCJfdXNlTWVyZ2VTdGF0ZSIsIl9pbnRlcm9wUmVxdWlyZURlZmF1bHQiLCJyZXF1aXJlIiwib2JqIiwicHJvcHMiLCJnRFNGUCIsImluaXRpYWxTdGF0ZSIsInN0YXRlIiwic2V0U3RhdGUiLCJuZXh0U3RhdGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergeStateFromProps.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergedRefs.js":
/*!***************************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergedRefs.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nexports.mergeRefs = mergeRefs;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nconst toFnRef = (ref)=>!ref || typeof ref === \"function\" ? ref : (value)=>{\n        ref.current = value;\n    };\nfunction mergeRefs(refA, refB) {\n    const a = toFnRef(refA);\n    const b = toFnRef(refB);\n    return (value)=>{\n        if (a) a(value);\n        if (b) b(value);\n    };\n}\n/**\n * Create and returns a single callback ref composed from two other Refs.\n *\n * ```tsx\n * const Button = React.forwardRef((props, ref) => {\n *   const [element, attachRef] = useCallbackRef<HTMLButtonElement>();\n *   const mergedRef = useMergedRefs(ref, attachRef);\n *\n *   return <button ref={mergedRef} {...props}/>\n * })\n * ```\n *\n * @param refA A Callback or mutable Ref\n * @param refB A Callback or mutable Ref\n * @category refs\n */ function useMergedRefs(refA, refB) {\n    return (0, _react.useMemo)(()=>mergeRefs(refA, refB), [\n        refA,\n        refB\n    ]);\n}\nvar _default = useMergedRefs;\nexports[\"default\"] = _default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMergedRefs.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMounted.js":
/*!************************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMounted.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = useMounted;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/**\n * Track whether a component is current mounted. Generally less preferable than\n * properlly canceling effects so they don't run after a component is unmounted,\n * but helpful in cases where that isn't feasible, such as a `Promise` resolution.\n *\n * @returns a function that returns the current isMounted state of the component\n *\n * ```ts\n * const [data, setData] = useState(null)\n * const isMounted = useMounted()\n *\n * useEffect(() => {\n *   fetchdata().then((newData) => {\n *      if (isMounted()) {\n *        setData(newData);\n *      }\n *   })\n * })\n * ```\n */ function useMounted() {\n    const mounted = (0, _react.useRef)(true);\n    const isMounted = (0, _react.useRef)(()=>mounted.current);\n    (0, _react.useEffect)(()=>{\n        mounted.current = true;\n        return ()=>{\n            mounted.current = false;\n        };\n    }, []);\n    return isMounted.current;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useMounted.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/usePrevious.js":
/*!*************************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/usePrevious.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = usePrevious;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/**\n * Store the last of some value. Tracked via a `Ref` only updating it\n * after the component renders.\n *\n * Helpful if you need to compare a prop value to it's previous value during render.\n *\n * ```ts\n * function Component(props) {\n *   const lastProps = usePrevious(props)\n *\n *   if (lastProps.foo !== props.foo)\n *     resetValueFromProps(props.foo)\n * }\n * ```\n *\n * @param value the value to track\n */ function usePrevious(value) {\n    const ref = (0, _react.useRef)(null);\n    (0, _react.useEffect)(()=>{\n        ref.current = value;\n    });\n    return ref.current;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L3VpL25vZGVfbW9kdWxlcy9AcmVzdGFydC9ob29rcy9janMvdXNlUHJldmlvdXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFFYkEsa0JBQWtCLEdBQUc7QUFDckJBLGtCQUFlLEdBQUdHO0FBQ2xCLElBQUlDLFNBQVNDLG1CQUFPQSxDQUFDLDRHQUFPO0FBQzVCOzs7Ozs7Ozs7Ozs7Ozs7O0NBZ0JDLEdBQ0QsU0FBU0YsWUFBWUcsS0FBSztJQUN4QixNQUFNQyxNQUFNLENBQUMsR0FBR0gsT0FBT0ksTUFBTSxFQUFFO0lBQzlCLElBQUdKLE9BQU9LLFNBQVMsRUFBRTtRQUNwQkYsSUFBSUcsT0FBTyxHQUFHSjtJQUNoQjtJQUNBLE9BQU9DLElBQUlHLE9BQU87QUFDcEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvdWkvbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L2hvb2tzL2Nqcy91c2VQcmV2aW91cy5qcz82N2VkIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5leHBvcnRzLl9fZXNNb2R1bGUgPSB0cnVlO1xuZXhwb3J0cy5kZWZhdWx0ID0gdXNlUHJldmlvdXM7XG52YXIgX3JlYWN0ID0gcmVxdWlyZShcInJlYWN0XCIpO1xuLyoqXG4gKiBTdG9yZSB0aGUgbGFzdCBvZiBzb21lIHZhbHVlLiBUcmFja2VkIHZpYSBhIGBSZWZgIG9ubHkgdXBkYXRpbmcgaXRcbiAqIGFmdGVyIHRoZSBjb21wb25lbnQgcmVuZGVycy5cbiAqXG4gKiBIZWxwZnVsIGlmIHlvdSBuZWVkIHRvIGNvbXBhcmUgYSBwcm9wIHZhbHVlIHRvIGl0J3MgcHJldmlvdXMgdmFsdWUgZHVyaW5nIHJlbmRlci5cbiAqXG4gKiBgYGB0c1xuICogZnVuY3Rpb24gQ29tcG9uZW50KHByb3BzKSB7XG4gKiAgIGNvbnN0IGxhc3RQcm9wcyA9IHVzZVByZXZpb3VzKHByb3BzKVxuICpcbiAqICAgaWYgKGxhc3RQcm9wcy5mb28gIT09IHByb3BzLmZvbylcbiAqICAgICByZXNldFZhbHVlRnJvbVByb3BzKHByb3BzLmZvbylcbiAqIH1cbiAqIGBgYFxuICpcbiAqIEBwYXJhbSB2YWx1ZSB0aGUgdmFsdWUgdG8gdHJhY2tcbiAqL1xuZnVuY3Rpb24gdXNlUHJldmlvdXModmFsdWUpIHtcbiAgY29uc3QgcmVmID0gKDAsIF9yZWFjdC51c2VSZWYpKG51bGwpO1xuICAoMCwgX3JlYWN0LnVzZUVmZmVjdCkoKCkgPT4ge1xuICAgIHJlZi5jdXJyZW50ID0gdmFsdWU7XG4gIH0pO1xuICByZXR1cm4gcmVmLmN1cnJlbnQ7XG59Il0sIm5hbWVzIjpbImV4cG9ydHMiLCJfX2VzTW9kdWxlIiwiZGVmYXVsdCIsInVzZVByZXZpb3VzIiwiX3JlYWN0IiwicmVxdWlyZSIsInZhbHVlIiwicmVmIiwidXNlUmVmIiwidXNlRWZmZWN0IiwiY3VycmVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/usePrevious.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useRafInterval.js":
/*!****************************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useRafInterval.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = void 0;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar _useCommittedRef = _interopRequireDefault(__webpack_require__(/*! ./useCommittedRef */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useCommittedRef.js\"));\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction useRafInterval(fn, ms, paused = false) {\n    let handle;\n    let start = new Date().getTime();\n    const fnRef = (0, _useCommittedRef.default)(fn);\n    // this ref is necessary b/c useEffect will sometimes miss a paused toggle\n    // orphaning a setTimeout chain in the aether, so relying on it's refresh logic is not reliable.\n    const pausedRef = (0, _useCommittedRef.default)(paused);\n    function loop() {\n        const current = new Date().getTime();\n        const delta = current - start;\n        if (pausedRef.current) return;\n        if (delta >= ms && fnRef.current) {\n            fnRef.current();\n            start = new Date().getTime();\n        }\n        cancelAnimationFrame(handle);\n        handle = requestAnimationFrame(loop);\n    }\n    (0, _react.useEffect)(()=>{\n        handle = requestAnimationFrame(loop);\n        return ()=>cancelAnimationFrame(handle);\n    }, []);\n}\nvar _default = useRafInterval;\nexports[\"default\"] = _default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useRafInterval.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useResizeObserver.js":
/*!*******************************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useResizeObserver.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = useResizeObserver;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar _useIsomorphicEffect = _interopRequireDefault(__webpack_require__(/*! ./useIsomorphicEffect */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useIsomorphicEffect.js\"));\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nconst targetMap = new WeakMap();\nlet resizeObserver;\nfunction getResizeObserver() {\n    // eslint-disable-next-line no-return-assign\n    return resizeObserver = resizeObserver || new window.ResizeObserver((entries)=>{\n        entries.forEach((entry)=>{\n            const handler = targetMap.get(entry.target);\n            if (handler) handler(entry.contentRect);\n        });\n    });\n}\n/**\n * Efficiently observe size changes on an element. Depends on the `ResizeObserver` api,\n * and polyfills are needed in older browsers.\n *\n * ```ts\n * const [ref, attachRef] = useCallbackRef(null);\n *\n * const rect = useResizeObserver(ref);\n *\n * return (\n *  <div ref={attachRef}>\n *    {JSON.stringify(rect)}\n *  </div>\n * )\n * ```\n *\n * @param element The DOM element to observe\n */ function useResizeObserver(element) {\n    const [rect, setRect] = (0, _react.useState)(null);\n    (0, _useIsomorphicEffect.default)(()=>{\n        if (!element) return;\n        getResizeObserver().observe(element);\n        setRect(element.getBoundingClientRect());\n        targetMap.set(element, (rect)=>{\n            setRect(rect);\n        });\n        return ()=>{\n            targetMap.delete(element);\n        };\n    }, [\n        element\n    ]);\n    return rect;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useResizeObserver.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useUpdatedRef.js":
/*!***************************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useUpdatedRef.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = useUpdatedRef;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/**\n * Returns a ref that is immediately updated with the new value\n *\n * @param value The Ref value\n * @category refs\n */ function useUpdatedRef(value) {\n    const valueRef = (0, _react.useRef)(value);\n    valueRef.current = value;\n    return valueRef;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L3VpL25vZGVfbW9kdWxlcy9AcmVzdGFydC9ob29rcy9janMvdXNlVXBkYXRlZFJlZi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUViQSxrQkFBa0IsR0FBRztBQUNyQkEsa0JBQWUsR0FBR0c7QUFDbEIsSUFBSUMsU0FBU0MsbUJBQU9BLENBQUMsNEdBQU87QUFDNUI7Ozs7O0NBS0MsR0FDRCxTQUFTRixjQUFjRyxLQUFLO0lBQzFCLE1BQU1DLFdBQVcsQ0FBQyxHQUFHSCxPQUFPSSxNQUFNLEVBQUVGO0lBQ3BDQyxTQUFTRSxPQUFPLEdBQUdIO0lBQ25CLE9BQU9DO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJ1dGktdGVjaC8uLi8uLi9ub2RlX21vZHVsZXMvQHJlc3RhcnQvdWkvbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L2hvb2tzL2Nqcy91c2VVcGRhdGVkUmVmLmpzP2QwZTAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbmV4cG9ydHMuX19lc01vZHVsZSA9IHRydWU7XG5leHBvcnRzLmRlZmF1bHQgPSB1c2VVcGRhdGVkUmVmO1xudmFyIF9yZWFjdCA9IHJlcXVpcmUoXCJyZWFjdFwiKTtcbi8qKlxuICogUmV0dXJucyBhIHJlZiB0aGF0IGlzIGltbWVkaWF0ZWx5IHVwZGF0ZWQgd2l0aCB0aGUgbmV3IHZhbHVlXG4gKlxuICogQHBhcmFtIHZhbHVlIFRoZSBSZWYgdmFsdWVcbiAqIEBjYXRlZ29yeSByZWZzXG4gKi9cbmZ1bmN0aW9uIHVzZVVwZGF0ZWRSZWYodmFsdWUpIHtcbiAgY29uc3QgdmFsdWVSZWYgPSAoMCwgX3JlYWN0LnVzZVJlZikodmFsdWUpO1xuICB2YWx1ZVJlZi5jdXJyZW50ID0gdmFsdWU7XG4gIHJldHVybiB2YWx1ZVJlZjtcbn0iXSwibmFtZXMiOlsiZXhwb3J0cyIsIl9fZXNNb2R1bGUiLCJkZWZhdWx0IiwidXNlVXBkYXRlZFJlZiIsIl9yZWFjdCIsInJlcXVpcmUiLCJ2YWx1ZSIsInZhbHVlUmVmIiwidXNlUmVmIiwiY3VycmVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useUpdatedRef.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useWillUnmount.js":
/*!****************************************************************************************!*\
  !*** ../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useWillUnmount.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.__esModule = true;\nexports[\"default\"] = useWillUnmount;\nvar _useUpdatedRef = _interopRequireDefault(__webpack_require__(/*! ./useUpdatedRef */ \"(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useUpdatedRef.js\"));\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\n/**\n * Attach a callback that fires when a component unmounts\n *\n * @param fn Handler to run when the component unmounts\n * @deprecated Use `useMounted` and normal effects, this is not StrictMode safe\n * @category effects\n */ function useWillUnmount(fn) {\n    const onUnmount = (0, _useUpdatedRef.default)(fn);\n    (0, _react.useEffect)(()=>()=>onUnmount.current(), []);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L3VpL25vZGVfbW9kdWxlcy9AcmVzdGFydC9ob29rcy9janMvdXNlV2lsbFVubW91bnQuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFFYkEsa0JBQWtCLEdBQUc7QUFDckJBLGtCQUFlLEdBQUdHO0FBQ2xCLElBQUlDLGlCQUFpQkMsdUJBQXVCQyxtQkFBT0EsQ0FBQyw4R0FBaUI7QUFDckUsSUFBSUMsU0FBU0QsbUJBQU9BLENBQUMsNEdBQU87QUFDNUIsU0FBU0QsdUJBQXVCRyxHQUFHO0lBQUksT0FBT0EsT0FBT0EsSUFBSVAsVUFBVSxHQUFHTyxNQUFNO1FBQUVOLFNBQVNNO0lBQUk7QUFBRztBQUM5Rjs7Ozs7O0NBTUMsR0FDRCxTQUFTTCxlQUFlTSxFQUFFO0lBQ3hCLE1BQU1DLFlBQVksQ0FBQyxHQUFHTixlQUFlRixPQUFPLEVBQUVPO0lBQzdDLElBQUdGLE9BQU9JLFNBQVMsRUFBRSxJQUFNLElBQU1ELFVBQVVFLE9BQU8sSUFBSSxFQUFFO0FBQzNEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFydXRpLXRlY2gvLi4vLi4vbm9kZV9tb2R1bGVzL0ByZXN0YXJ0L3VpL25vZGVfbW9kdWxlcy9AcmVzdGFydC9ob29rcy9janMvdXNlV2lsbFVubW91bnQuanM/ZGQ2ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuZXhwb3J0cy5fX2VzTW9kdWxlID0gdHJ1ZTtcbmV4cG9ydHMuZGVmYXVsdCA9IHVzZVdpbGxVbm1vdW50O1xudmFyIF91c2VVcGRhdGVkUmVmID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi91c2VVcGRhdGVkUmVmXCIpKTtcbnZhciBfcmVhY3QgPSByZXF1aXJlKFwicmVhY3RcIik7XG5mdW5jdGlvbiBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KG9iaikgeyByZXR1cm4gb2JqICYmIG9iai5fX2VzTW9kdWxlID8gb2JqIDogeyBkZWZhdWx0OiBvYmogfTsgfVxuLyoqXG4gKiBBdHRhY2ggYSBjYWxsYmFjayB0aGF0IGZpcmVzIHdoZW4gYSBjb21wb25lbnQgdW5tb3VudHNcbiAqXG4gKiBAcGFyYW0gZm4gSGFuZGxlciB0byBydW4gd2hlbiB0aGUgY29tcG9uZW50IHVubW91bnRzXG4gKiBAZGVwcmVjYXRlZCBVc2UgYHVzZU1vdW50ZWRgIGFuZCBub3JtYWwgZWZmZWN0cywgdGhpcyBpcyBub3QgU3RyaWN0TW9kZSBzYWZlXG4gKiBAY2F0ZWdvcnkgZWZmZWN0c1xuICovXG5mdW5jdGlvbiB1c2VXaWxsVW5tb3VudChmbikge1xuICBjb25zdCBvblVubW91bnQgPSAoMCwgX3VzZVVwZGF0ZWRSZWYuZGVmYXVsdCkoZm4pO1xuICAoMCwgX3JlYWN0LnVzZUVmZmVjdCkoKCkgPT4gKCkgPT4gb25Vbm1vdW50LmN1cnJlbnQoKSwgW10pO1xufSJdLCJuYW1lcyI6WyJleHBvcnRzIiwiX19lc01vZHVsZSIsImRlZmF1bHQiLCJ1c2VXaWxsVW5tb3VudCIsIl91c2VVcGRhdGVkUmVmIiwiX2ludGVyb3BSZXF1aXJlRGVmYXVsdCIsInJlcXVpcmUiLCJfcmVhY3QiLCJvYmoiLCJmbiIsIm9uVW5tb3VudCIsInVzZUVmZmVjdCIsImN1cnJlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@restart/ui/node_modules/@restart/hooks/cjs/useWillUnmount.js\n");

/***/ })

};
;