"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/embla-carousel";
exports.ids = ["vendor-chunks/embla-carousel"];
exports.modules = {

/***/ "(ssr)/../../node_modules/embla-carousel/esm/embla-carousel.esm.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/embla-carousel/esm/embla-carousel.esm.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EmblaCarousel)\n/* harmony export */ });\nfunction isNumber(subject) {\n    return typeof subject === \"number\";\n}\nfunction isString(subject) {\n    return typeof subject === \"string\";\n}\nfunction isBoolean(subject) {\n    return typeof subject === \"boolean\";\n}\nfunction isObject(subject) {\n    return Object.prototype.toString.call(subject) === \"[object Object]\";\n}\nfunction mathAbs(n) {\n    return Math.abs(n);\n}\nfunction mathSign(n) {\n    return Math.sign(n);\n}\nfunction deltaAbs(valueB, valueA) {\n    return mathAbs(valueB - valueA);\n}\nfunction factorAbs(valueB, valueA) {\n    if (valueB === 0 || valueA === 0) return 0;\n    if (mathAbs(valueB) <= mathAbs(valueA)) return 0;\n    const diff = deltaAbs(mathAbs(valueB), mathAbs(valueA));\n    return mathAbs(diff / valueB);\n}\nfunction arrayKeys(array) {\n    return objectKeys(array).map(Number);\n}\nfunction arrayLast(array) {\n    return array[arrayLastIndex(array)];\n}\nfunction arrayLastIndex(array) {\n    return Math.max(0, array.length - 1);\n}\nfunction arrayIsLastIndex(array, index) {\n    return index === arrayLastIndex(array);\n}\nfunction arrayFromNumber(n, startAt = 0) {\n    return Array.from(Array(n), (_, i)=>startAt + i);\n}\nfunction objectKeys(object) {\n    return Object.keys(object);\n}\nfunction objectsMergeDeep(objectA, objectB) {\n    return [\n        objectA,\n        objectB\n    ].reduce((mergedObjects, currentObject)=>{\n        objectKeys(currentObject).forEach((key)=>{\n            const valueA = mergedObjects[key];\n            const valueB = currentObject[key];\n            const areObjects = isObject(valueA) && isObject(valueB);\n            mergedObjects[key] = areObjects ? objectsMergeDeep(valueA, valueB) : valueB;\n        });\n        return mergedObjects;\n    }, {});\n}\nfunction isMouseEvent(evt, ownerWindow) {\n    return typeof ownerWindow.MouseEvent !== \"undefined\" && evt instanceof ownerWindow.MouseEvent;\n}\nfunction Alignment(align, viewSize) {\n    const predefined = {\n        start,\n        center,\n        end\n    };\n    function start() {\n        return 0;\n    }\n    function center(n) {\n        return end(n) / 2;\n    }\n    function end(n) {\n        return viewSize - n;\n    }\n    function measure(n, index) {\n        if (isString(align)) return predefined[align](n);\n        return align(viewSize, n, index);\n    }\n    const self = {\n        measure\n    };\n    return self;\n}\nfunction EventStore() {\n    let listeners = [];\n    function add(node, type, handler, options = {\n        passive: true\n    }) {\n        let removeListener;\n        if (\"addEventListener\" in node) {\n            node.addEventListener(type, handler, options);\n            removeListener = ()=>node.removeEventListener(type, handler, options);\n        } else {\n            const legacyMediaQueryList = node;\n            legacyMediaQueryList.addListener(handler);\n            removeListener = ()=>legacyMediaQueryList.removeListener(handler);\n        }\n        listeners.push(removeListener);\n        return self;\n    }\n    function clear() {\n        listeners = listeners.filter((remove)=>remove());\n    }\n    const self = {\n        add,\n        clear\n    };\n    return self;\n}\nfunction Animations(ownerDocument, ownerWindow, update, render) {\n    const documentVisibleHandler = EventStore();\n    const timeStep = 1000 / 60;\n    let lastTimeStamp = null;\n    let lag = 0;\n    let animationFrame = 0;\n    function init() {\n        documentVisibleHandler.add(ownerDocument, \"visibilitychange\", ()=>{\n            if (ownerDocument.hidden) reset();\n        });\n    }\n    function destroy() {\n        stop();\n        documentVisibleHandler.clear();\n    }\n    function animate(timeStamp) {\n        if (!animationFrame) return;\n        if (!lastTimeStamp) lastTimeStamp = timeStamp;\n        const elapsed = timeStamp - lastTimeStamp;\n        lastTimeStamp = timeStamp;\n        lag += elapsed;\n        while(lag >= timeStep){\n            update();\n            lag -= timeStep;\n        }\n        const lagOffset = mathAbs(lag / timeStep);\n        render(lagOffset);\n        if (animationFrame) ownerWindow.requestAnimationFrame(animate);\n    }\n    function start() {\n        if (animationFrame) return;\n        animationFrame = ownerWindow.requestAnimationFrame(animate);\n    }\n    function stop() {\n        ownerWindow.cancelAnimationFrame(animationFrame);\n        lastTimeStamp = null;\n        lag = 0;\n        animationFrame = 0;\n    }\n    function reset() {\n        lastTimeStamp = null;\n        lag = 0;\n    }\n    const self = {\n        init,\n        destroy,\n        start,\n        stop,\n        update,\n        render\n    };\n    return self;\n}\nfunction Axis(axis, contentDirection) {\n    const isRightToLeft = contentDirection === \"rtl\";\n    const isVertical = axis === \"y\";\n    const scroll = isVertical ? \"y\" : \"x\";\n    const cross = isVertical ? \"x\" : \"y\";\n    const sign = !isVertical && isRightToLeft ? -1 : 1;\n    const startEdge = getStartEdge();\n    const endEdge = getEndEdge();\n    function measureSize(nodeRect) {\n        const { height, width } = nodeRect;\n        return isVertical ? height : width;\n    }\n    function getStartEdge() {\n        if (isVertical) return \"top\";\n        return isRightToLeft ? \"right\" : \"left\";\n    }\n    function getEndEdge() {\n        if (isVertical) return \"bottom\";\n        return isRightToLeft ? \"left\" : \"right\";\n    }\n    function direction(n) {\n        return n * sign;\n    }\n    const self = {\n        scroll,\n        cross,\n        startEdge,\n        endEdge,\n        measureSize,\n        direction\n    };\n    return self;\n}\nfunction Limit(min = 0, max = 0) {\n    const length = mathAbs(min - max);\n    function reachedMin(n) {\n        return n < min;\n    }\n    function reachedMax(n) {\n        return n > max;\n    }\n    function reachedAny(n) {\n        return reachedMin(n) || reachedMax(n);\n    }\n    function constrain(n) {\n        if (!reachedAny(n)) return n;\n        return reachedMin(n) ? min : max;\n    }\n    function removeOffset(n) {\n        if (!length) return n;\n        return n - length * Math.ceil((n - max) / length);\n    }\n    const self = {\n        length,\n        max,\n        min,\n        constrain,\n        reachedAny,\n        reachedMax,\n        reachedMin,\n        removeOffset\n    };\n    return self;\n}\nfunction Counter(max, start, loop) {\n    const { constrain } = Limit(0, max);\n    const loopEnd = max + 1;\n    let counter = withinLimit(start);\n    function withinLimit(n) {\n        return !loop ? constrain(n) : mathAbs((loopEnd + n) % loopEnd);\n    }\n    function get() {\n        return counter;\n    }\n    function set(n) {\n        counter = withinLimit(n);\n        return self;\n    }\n    function add(n) {\n        return clone().set(get() + n);\n    }\n    function clone() {\n        return Counter(max, get(), loop);\n    }\n    const self = {\n        get,\n        set,\n        add,\n        clone\n    };\n    return self;\n}\nfunction DragHandler(axis, rootNode, ownerDocument, ownerWindow, target, dragTracker, location, animation, scrollTo, scrollBody, scrollTarget, index, eventHandler, percentOfView, dragFree, dragThreshold, skipSnaps, baseFriction, watchDrag) {\n    const { cross: crossAxis, direction } = axis;\n    const focusNodes = [\n        \"INPUT\",\n        \"SELECT\",\n        \"TEXTAREA\"\n    ];\n    const nonPassiveEvent = {\n        passive: false\n    };\n    const initEvents = EventStore();\n    const dragEvents = EventStore();\n    const goToNextThreshold = Limit(50, 225).constrain(percentOfView.measure(20));\n    const snapForceBoost = {\n        mouse: 300,\n        touch: 400\n    };\n    const freeForceBoost = {\n        mouse: 500,\n        touch: 600\n    };\n    const baseSpeed = dragFree ? 43 : 25;\n    let isMoving = false;\n    let startScroll = 0;\n    let startCross = 0;\n    let pointerIsDown = false;\n    let preventScroll = false;\n    let preventClick = false;\n    let isMouse = false;\n    function init(emblaApi) {\n        if (!watchDrag) return;\n        function downIfAllowed(evt) {\n            if (isBoolean(watchDrag) || watchDrag(emblaApi, evt)) down(evt);\n        }\n        const node = rootNode;\n        initEvents.add(node, \"dragstart\", (evt)=>evt.preventDefault(), nonPassiveEvent).add(node, \"touchmove\", ()=>undefined, nonPassiveEvent).add(node, \"touchend\", ()=>undefined).add(node, \"touchstart\", downIfAllowed).add(node, \"mousedown\", downIfAllowed).add(node, \"touchcancel\", up).add(node, \"contextmenu\", up).add(node, \"click\", click, true);\n    }\n    function destroy() {\n        initEvents.clear();\n        dragEvents.clear();\n    }\n    function addDragEvents() {\n        const node = isMouse ? ownerDocument : rootNode;\n        dragEvents.add(node, \"touchmove\", move, nonPassiveEvent).add(node, \"touchend\", up).add(node, \"mousemove\", move, nonPassiveEvent).add(node, \"mouseup\", up);\n    }\n    function isFocusNode(node) {\n        const nodeName = node.nodeName || \"\";\n        return focusNodes.includes(nodeName);\n    }\n    function forceBoost() {\n        const boost = dragFree ? freeForceBoost : snapForceBoost;\n        const type = isMouse ? \"mouse\" : \"touch\";\n        return boost[type];\n    }\n    function allowedForce(force, targetChanged) {\n        const next = index.add(mathSign(force) * -1);\n        const baseForce = scrollTarget.byDistance(force, !dragFree).distance;\n        if (dragFree || mathAbs(force) < goToNextThreshold) return baseForce;\n        if (skipSnaps && targetChanged) return baseForce * 0.5;\n        return scrollTarget.byIndex(next.get(), 0).distance;\n    }\n    function down(evt) {\n        const isMouseEvt = isMouseEvent(evt, ownerWindow);\n        isMouse = isMouseEvt;\n        preventClick = dragFree && isMouseEvt && !evt.buttons && isMoving;\n        isMoving = deltaAbs(target.get(), location.get()) >= 2;\n        if (isMouseEvt && evt.button !== 0) return;\n        if (isFocusNode(evt.target)) return;\n        pointerIsDown = true;\n        dragTracker.pointerDown(evt);\n        scrollBody.useFriction(0).useDuration(0);\n        target.set(location);\n        addDragEvents();\n        startScroll = dragTracker.readPoint(evt);\n        startCross = dragTracker.readPoint(evt, crossAxis);\n        eventHandler.emit(\"pointerDown\");\n    }\n    function move(evt) {\n        const isTouchEvt = !isMouseEvent(evt, ownerWindow);\n        if (isTouchEvt && evt.touches.length >= 2) return up(evt);\n        const lastScroll = dragTracker.readPoint(evt);\n        const lastCross = dragTracker.readPoint(evt, crossAxis);\n        const diffScroll = deltaAbs(lastScroll, startScroll);\n        const diffCross = deltaAbs(lastCross, startCross);\n        if (!preventScroll && !isMouse) {\n            if (!evt.cancelable) return up(evt);\n            preventScroll = diffScroll > diffCross;\n            if (!preventScroll) return up(evt);\n        }\n        const diff = dragTracker.pointerMove(evt);\n        if (diffScroll > dragThreshold) preventClick = true;\n        scrollBody.useFriction(0.3).useDuration(0.75);\n        animation.start();\n        target.add(direction(diff));\n        evt.preventDefault();\n    }\n    function up(evt) {\n        const currentLocation = scrollTarget.byDistance(0, false);\n        const targetChanged = currentLocation.index !== index.get();\n        const rawForce = dragTracker.pointerUp(evt) * forceBoost();\n        const force = allowedForce(direction(rawForce), targetChanged);\n        const forceFactor = factorAbs(rawForce, force);\n        const speed = baseSpeed - 10 * forceFactor;\n        const friction = baseFriction + forceFactor / 50;\n        preventScroll = false;\n        pointerIsDown = false;\n        dragEvents.clear();\n        scrollBody.useDuration(speed).useFriction(friction);\n        scrollTo.distance(force, !dragFree);\n        isMouse = false;\n        eventHandler.emit(\"pointerUp\");\n    }\n    function click(evt) {\n        if (preventClick) {\n            evt.stopPropagation();\n            evt.preventDefault();\n            preventClick = false;\n        }\n    }\n    function pointerDown() {\n        return pointerIsDown;\n    }\n    const self = {\n        init,\n        destroy,\n        pointerDown\n    };\n    return self;\n}\nfunction DragTracker(axis, ownerWindow) {\n    const logInterval = 170;\n    let startEvent;\n    let lastEvent;\n    function readTime(evt) {\n        return evt.timeStamp;\n    }\n    function readPoint(evt, evtAxis) {\n        const property = evtAxis || axis.scroll;\n        const coord = `client${property === \"x\" ? \"X\" : \"Y\"}`;\n        return (isMouseEvent(evt, ownerWindow) ? evt : evt.touches[0])[coord];\n    }\n    function pointerDown(evt) {\n        startEvent = evt;\n        lastEvent = evt;\n        return readPoint(evt);\n    }\n    function pointerMove(evt) {\n        const diff = readPoint(evt) - readPoint(lastEvent);\n        const expired = readTime(evt) - readTime(startEvent) > logInterval;\n        lastEvent = evt;\n        if (expired) startEvent = evt;\n        return diff;\n    }\n    function pointerUp(evt) {\n        if (!startEvent || !lastEvent) return 0;\n        const diffDrag = readPoint(lastEvent) - readPoint(startEvent);\n        const diffTime = readTime(evt) - readTime(startEvent);\n        const expired = readTime(evt) - readTime(lastEvent) > logInterval;\n        const force = diffDrag / diffTime;\n        const isFlick = diffTime && !expired && mathAbs(force) > 0.1;\n        return isFlick ? force : 0;\n    }\n    const self = {\n        pointerDown,\n        pointerMove,\n        pointerUp,\n        readPoint\n    };\n    return self;\n}\nfunction NodeRects() {\n    function measure(node) {\n        const { offsetTop, offsetLeft, offsetWidth, offsetHeight } = node;\n        const offset = {\n            top: offsetTop,\n            right: offsetLeft + offsetWidth,\n            bottom: offsetTop + offsetHeight,\n            left: offsetLeft,\n            width: offsetWidth,\n            height: offsetHeight\n        };\n        return offset;\n    }\n    const self = {\n        measure\n    };\n    return self;\n}\nfunction PercentOfView(viewSize) {\n    function measure(n) {\n        return viewSize * (n / 100);\n    }\n    const self = {\n        measure\n    };\n    return self;\n}\nfunction ResizeHandler(container, eventHandler, ownerWindow, slides, axis, watchResize, nodeRects) {\n    let resizeObserver;\n    let containerSize;\n    let slideSizes = [];\n    let destroyed = false;\n    function readSize(node) {\n        return axis.measureSize(nodeRects.measure(node));\n    }\n    function init(emblaApi) {\n        if (!watchResize) return;\n        containerSize = readSize(container);\n        slideSizes = slides.map(readSize);\n        function defaultCallback(entries) {\n            for (const entry of entries){\n                const isContainer = entry.target === container;\n                const slideIndex = slides.indexOf(entry.target);\n                const lastSize = isContainer ? containerSize : slideSizes[slideIndex];\n                const newSize = readSize(isContainer ? container : slides[slideIndex]);\n                const diffSize = mathAbs(newSize - lastSize);\n                if (diffSize >= 0.5) {\n                    ownerWindow.requestAnimationFrame(()=>{\n                        emblaApi.reInit();\n                        eventHandler.emit(\"resize\");\n                    });\n                    break;\n                }\n            }\n        }\n        resizeObserver = new ResizeObserver((entries)=>{\n            if (destroyed) return;\n            if (isBoolean(watchResize) || watchResize(emblaApi, entries)) {\n                defaultCallback(entries);\n            }\n        });\n        const observeNodes = [\n            container\n        ].concat(slides);\n        observeNodes.forEach((node)=>resizeObserver.observe(node));\n    }\n    function destroy() {\n        if (resizeObserver) resizeObserver.disconnect();\n        destroyed = true;\n    }\n    const self = {\n        init,\n        destroy\n    };\n    return self;\n}\nfunction ScrollBody(location, offsetLocation, target, baseDuration, baseFriction) {\n    let bodyVelocity = 0;\n    let scrollDirection = 0;\n    let scrollDuration = baseDuration;\n    let scrollFriction = baseFriction;\n    let rawLocation = location.get();\n    let rawLocationPrevious = 0;\n    function seek() {\n        const diff = target.get() - location.get();\n        const isInstant = !scrollDuration;\n        let directionDiff = 0;\n        if (isInstant) {\n            bodyVelocity = 0;\n            location.set(target);\n            directionDiff = diff;\n        } else {\n            bodyVelocity += diff / scrollDuration;\n            bodyVelocity *= scrollFriction;\n            rawLocation += bodyVelocity;\n            location.add(bodyVelocity);\n            directionDiff = rawLocation - rawLocationPrevious;\n        }\n        scrollDirection = mathSign(directionDiff);\n        rawLocationPrevious = rawLocation;\n        return self;\n    }\n    function settled() {\n        const diff = target.get() - offsetLocation.get();\n        return mathAbs(diff) < 0.001;\n    }\n    function duration() {\n        return scrollDuration;\n    }\n    function direction() {\n        return scrollDirection;\n    }\n    function velocity() {\n        return bodyVelocity;\n    }\n    function useBaseDuration() {\n        return useDuration(baseDuration);\n    }\n    function useBaseFriction() {\n        return useFriction(baseFriction);\n    }\n    function useDuration(n) {\n        scrollDuration = n;\n        return self;\n    }\n    function useFriction(n) {\n        scrollFriction = n;\n        return self;\n    }\n    const self = {\n        direction,\n        duration,\n        velocity,\n        seek,\n        settled,\n        useBaseFriction,\n        useBaseDuration,\n        useFriction,\n        useDuration\n    };\n    return self;\n}\nfunction ScrollBounds(limit, offsetLocation, target, scrollBody, percentOfView) {\n    const pullBackThreshold = percentOfView.measure(10);\n    const edgeOffsetTolerance = percentOfView.measure(50);\n    const frictionLimit = Limit(0.1, 0.99);\n    let disabled = false;\n    function shouldConstrain() {\n        if (disabled) return false;\n        if (!limit.reachedAny(target.get())) return false;\n        if (!limit.reachedAny(offsetLocation.get())) return false;\n        return true;\n    }\n    function constrain(pointerDown) {\n        if (!shouldConstrain()) return;\n        const edge = limit.reachedMin(offsetLocation.get()) ? \"min\" : \"max\";\n        const diffToEdge = mathAbs(limit[edge] - offsetLocation.get());\n        const diffToTarget = target.get() - offsetLocation.get();\n        const friction = frictionLimit.constrain(diffToEdge / edgeOffsetTolerance);\n        target.subtract(diffToTarget * friction);\n        if (!pointerDown && mathAbs(diffToTarget) < pullBackThreshold) {\n            target.set(limit.constrain(target.get()));\n            scrollBody.useDuration(25).useBaseFriction();\n        }\n    }\n    function toggleActive(active) {\n        disabled = !active;\n    }\n    const self = {\n        constrain,\n        toggleActive\n    };\n    return self;\n}\nfunction ScrollContain(viewSize, contentSize, snapsAligned, containScroll, pixelTolerance) {\n    const scrollBounds = Limit(-contentSize + viewSize, 0);\n    const snapsBounded = measureBounded();\n    const scrollContainLimit = findScrollContainLimit();\n    const snapsContained = measureContained();\n    function usePixelTolerance(bound, snap) {\n        return deltaAbs(bound, snap) < 1;\n    }\n    function findScrollContainLimit() {\n        const startSnap = snapsBounded[0];\n        const endSnap = arrayLast(snapsBounded);\n        const min = snapsBounded.lastIndexOf(startSnap);\n        const max = snapsBounded.indexOf(endSnap) + 1;\n        return Limit(min, max);\n    }\n    function measureBounded() {\n        return snapsAligned.map((snapAligned, index)=>{\n            const { min, max } = scrollBounds;\n            const snap = scrollBounds.constrain(snapAligned);\n            const isFirst = !index;\n            const isLast = arrayIsLastIndex(snapsAligned, index);\n            if (isFirst) return max;\n            if (isLast) return min;\n            if (usePixelTolerance(min, snap)) return min;\n            if (usePixelTolerance(max, snap)) return max;\n            return snap;\n        }).map((scrollBound)=>parseFloat(scrollBound.toFixed(3)));\n    }\n    function measureContained() {\n        if (contentSize <= viewSize + pixelTolerance) return [\n            scrollBounds.max\n        ];\n        if (containScroll === \"keepSnaps\") return snapsBounded;\n        const { min, max } = scrollContainLimit;\n        return snapsBounded.slice(min, max);\n    }\n    const self = {\n        snapsContained,\n        scrollContainLimit\n    };\n    return self;\n}\nfunction ScrollLimit(contentSize, scrollSnaps, loop) {\n    const max = scrollSnaps[0];\n    const min = loop ? max - contentSize : arrayLast(scrollSnaps);\n    const limit = Limit(min, max);\n    const self = {\n        limit\n    };\n    return self;\n}\nfunction ScrollLooper(contentSize, limit, offsetLocation, vectors) {\n    const jointSafety = 0.1;\n    const min = limit.min + jointSafety;\n    const max = limit.max + jointSafety;\n    const { reachedMin, reachedMax } = Limit(min, max);\n    function shouldLoop(direction) {\n        if (direction === 1) return reachedMax(offsetLocation.get());\n        if (direction === -1) return reachedMin(offsetLocation.get());\n        return false;\n    }\n    function loop(direction) {\n        if (!shouldLoop(direction)) return;\n        const loopDistance = contentSize * (direction * -1);\n        vectors.forEach((v)=>v.add(loopDistance));\n    }\n    const self = {\n        loop\n    };\n    return self;\n}\nfunction ScrollProgress(limit) {\n    const { max, length } = limit;\n    function get(n) {\n        const currentLocation = n - max;\n        return length ? currentLocation / -length : 0;\n    }\n    const self = {\n        get\n    };\n    return self;\n}\nfunction ScrollSnaps(axis, alignment, containerRect, slideRects, slidesToScroll) {\n    const { startEdge, endEdge } = axis;\n    const { groupSlides } = slidesToScroll;\n    const alignments = measureSizes().map(alignment.measure);\n    const snaps = measureUnaligned();\n    const snapsAligned = measureAligned();\n    function measureSizes() {\n        return groupSlides(slideRects).map((rects)=>arrayLast(rects)[endEdge] - rects[0][startEdge]).map(mathAbs);\n    }\n    function measureUnaligned() {\n        return slideRects.map((rect)=>containerRect[startEdge] - rect[startEdge]).map((snap)=>-mathAbs(snap));\n    }\n    function measureAligned() {\n        return groupSlides(snaps).map((g)=>g[0]).map((snap, index)=>snap + alignments[index]);\n    }\n    const self = {\n        snaps,\n        snapsAligned\n    };\n    return self;\n}\nfunction SlideRegistry(containSnaps, containScroll, scrollSnaps, scrollContainLimit, slidesToScroll, slideIndexes) {\n    const { groupSlides } = slidesToScroll;\n    const { min, max } = scrollContainLimit;\n    const slideRegistry = createSlideRegistry();\n    function createSlideRegistry() {\n        const groupedSlideIndexes = groupSlides(slideIndexes);\n        const doNotContain = !containSnaps || containScroll === \"keepSnaps\";\n        if (scrollSnaps.length === 1) return [\n            slideIndexes\n        ];\n        if (doNotContain) return groupedSlideIndexes;\n        return groupedSlideIndexes.slice(min, max).map((group, index, groups)=>{\n            const isFirst = !index;\n            const isLast = arrayIsLastIndex(groups, index);\n            if (isFirst) {\n                const range = arrayLast(groups[0]) + 1;\n                return arrayFromNumber(range);\n            }\n            if (isLast) {\n                const range = arrayLastIndex(slideIndexes) - arrayLast(groups)[0] + 1;\n                return arrayFromNumber(range, arrayLast(groups)[0]);\n            }\n            return group;\n        });\n    }\n    const self = {\n        slideRegistry\n    };\n    return self;\n}\nfunction ScrollTarget(loop, scrollSnaps, contentSize, limit, targetVector) {\n    const { reachedAny, removeOffset, constrain } = limit;\n    function minDistance(distances) {\n        return distances.concat().sort((a, b)=>mathAbs(a) - mathAbs(b))[0];\n    }\n    function findTargetSnap(target) {\n        const distance = loop ? removeOffset(target) : constrain(target);\n        const ascDiffsToSnaps = scrollSnaps.map((snap, index)=>({\n                diff: shortcut(snap - distance, 0),\n                index\n            })).sort((d1, d2)=>mathAbs(d1.diff) - mathAbs(d2.diff));\n        const { index } = ascDiffsToSnaps[0];\n        return {\n            index,\n            distance\n        };\n    }\n    function shortcut(target, direction) {\n        const targets = [\n            target,\n            target + contentSize,\n            target - contentSize\n        ];\n        if (!loop) return target;\n        if (!direction) return minDistance(targets);\n        const matchingTargets = targets.filter((t)=>mathSign(t) === direction);\n        if (matchingTargets.length) return minDistance(matchingTargets);\n        return arrayLast(targets) - contentSize;\n    }\n    function byIndex(index, direction) {\n        const diffToSnap = scrollSnaps[index] - targetVector.get();\n        const distance = shortcut(diffToSnap, direction);\n        return {\n            index,\n            distance\n        };\n    }\n    function byDistance(distance, snap) {\n        const target = targetVector.get() + distance;\n        const { index, distance: targetSnapDistance } = findTargetSnap(target);\n        const reachedBound = !loop && reachedAny(target);\n        if (!snap || reachedBound) return {\n            index,\n            distance\n        };\n        const diffToSnap = scrollSnaps[index] - targetSnapDistance;\n        const snapDistance = distance + shortcut(diffToSnap, 0);\n        return {\n            index,\n            distance: snapDistance\n        };\n    }\n    const self = {\n        byDistance,\n        byIndex,\n        shortcut\n    };\n    return self;\n}\nfunction ScrollTo(animation, indexCurrent, indexPrevious, scrollBody, scrollTarget, targetVector, eventHandler) {\n    function scrollTo(target) {\n        const distanceDiff = target.distance;\n        const indexDiff = target.index !== indexCurrent.get();\n        targetVector.add(distanceDiff);\n        if (distanceDiff) {\n            if (scrollBody.duration()) {\n                animation.start();\n            } else {\n                animation.update();\n                animation.render(1);\n                animation.update();\n            }\n        }\n        if (indexDiff) {\n            indexPrevious.set(indexCurrent.get());\n            indexCurrent.set(target.index);\n            eventHandler.emit(\"select\");\n        }\n    }\n    function distance(n, snap) {\n        const target = scrollTarget.byDistance(n, snap);\n        scrollTo(target);\n    }\n    function index(n, direction) {\n        const targetIndex = indexCurrent.clone().set(n);\n        const target = scrollTarget.byIndex(targetIndex.get(), direction);\n        scrollTo(target);\n    }\n    const self = {\n        distance,\n        index\n    };\n    return self;\n}\nfunction SlideFocus(root, slides, slideRegistry, scrollTo, scrollBody, eventStore, eventHandler) {\n    let lastTabPressTime = 0;\n    function init() {\n        eventStore.add(document, \"keydown\", registerTabPress, false);\n        slides.forEach(addSlideFocusEvent);\n    }\n    function registerTabPress(event) {\n        if (event.code === \"Tab\") lastTabPressTime = new Date().getTime();\n    }\n    function addSlideFocusEvent(slide) {\n        const focus = ()=>{\n            const nowTime = new Date().getTime();\n            const diffTime = nowTime - lastTabPressTime;\n            if (diffTime > 10) return;\n            root.scrollLeft = 0;\n            const index = slides.indexOf(slide);\n            const group = slideRegistry.findIndex((group)=>group.includes(index));\n            if (!isNumber(group)) return;\n            scrollBody.useDuration(0);\n            scrollTo.index(group, 0);\n            eventHandler.emit(\"slideFocus\");\n        };\n        eventStore.add(slide, \"focus\", focus, {\n            passive: true,\n            capture: true\n        });\n    }\n    const self = {\n        init\n    };\n    return self;\n}\nfunction Vector1D(initialValue) {\n    let value = initialValue;\n    function get() {\n        return value;\n    }\n    function set(n) {\n        value = normalizeInput(n);\n    }\n    function add(n) {\n        value += normalizeInput(n);\n    }\n    function subtract(n) {\n        value -= normalizeInput(n);\n    }\n    function normalizeInput(n) {\n        return isNumber(n) ? n : n.get();\n    }\n    const self = {\n        get,\n        set,\n        add,\n        subtract\n    };\n    return self;\n}\nfunction Translate(axis, container) {\n    const translate = axis.scroll === \"x\" ? x : y;\n    const containerStyle = container.style;\n    let disabled = false;\n    function x(n) {\n        return `translate3d(${n}px,0px,0px)`;\n    }\n    function y(n) {\n        return `translate3d(0px,${n}px,0px)`;\n    }\n    function to(target) {\n        if (disabled) return;\n        containerStyle.transform = translate(axis.direction(target));\n    }\n    function toggleActive(active) {\n        disabled = !active;\n    }\n    function clear() {\n        if (disabled) return;\n        containerStyle.transform = \"\";\n        if (!container.getAttribute(\"style\")) container.removeAttribute(\"style\");\n    }\n    const self = {\n        clear,\n        to,\n        toggleActive\n    };\n    return self;\n}\nfunction SlideLooper(axis, viewSize, contentSize, slideSizes, slideSizesWithGaps, snaps, scrollSnaps, offsetLocation, slides) {\n    const roundingSafety = 0.5;\n    const ascItems = arrayKeys(slideSizesWithGaps);\n    const descItems = arrayKeys(slideSizesWithGaps).reverse();\n    const loopPoints = startPoints().concat(endPoints());\n    function removeSlideSizes(indexes, from) {\n        return indexes.reduce((a, i)=>{\n            return a - slideSizesWithGaps[i];\n        }, from);\n    }\n    function slidesInGap(indexes, gap) {\n        return indexes.reduce((a, i)=>{\n            const remainingGap = removeSlideSizes(a, gap);\n            return remainingGap > 0 ? a.concat([\n                i\n            ]) : a;\n        }, []);\n    }\n    function findSlideBounds(offset) {\n        return snaps.map((snap, index)=>({\n                start: snap - slideSizes[index] + roundingSafety + offset,\n                end: snap + viewSize - roundingSafety + offset\n            }));\n    }\n    function findLoopPoints(indexes, offset, isEndEdge) {\n        const slideBounds = findSlideBounds(offset);\n        return indexes.map((index)=>{\n            const initial = isEndEdge ? 0 : -contentSize;\n            const altered = isEndEdge ? contentSize : 0;\n            const boundEdge = isEndEdge ? \"end\" : \"start\";\n            const loopPoint = slideBounds[index][boundEdge];\n            return {\n                index,\n                loopPoint,\n                slideLocation: Vector1D(-1),\n                translate: Translate(axis, slides[index]),\n                target: ()=>offsetLocation.get() > loopPoint ? initial : altered\n            };\n        });\n    }\n    function startPoints() {\n        const gap = scrollSnaps[0];\n        const indexes = slidesInGap(descItems, gap);\n        return findLoopPoints(indexes, contentSize, false);\n    }\n    function endPoints() {\n        const gap = viewSize - scrollSnaps[0] - 1;\n        const indexes = slidesInGap(ascItems, gap);\n        return findLoopPoints(indexes, -contentSize, true);\n    }\n    function canLoop() {\n        return loopPoints.every(({ index })=>{\n            const otherIndexes = ascItems.filter((i)=>i !== index);\n            return removeSlideSizes(otherIndexes, viewSize) <= 0.1;\n        });\n    }\n    function loop() {\n        loopPoints.forEach((loopPoint)=>{\n            const { target, translate, slideLocation } = loopPoint;\n            const shiftLocation = target();\n            if (shiftLocation === slideLocation.get()) return;\n            translate.to(shiftLocation);\n            slideLocation.set(shiftLocation);\n        });\n    }\n    function clear() {\n        loopPoints.forEach((loopPoint)=>loopPoint.translate.clear());\n    }\n    const self = {\n        canLoop,\n        clear,\n        loop,\n        loopPoints\n    };\n    return self;\n}\nfunction SlidesHandler(container, eventHandler, watchSlides) {\n    let mutationObserver;\n    let destroyed = false;\n    function init(emblaApi) {\n        if (!watchSlides) return;\n        function defaultCallback(mutations) {\n            for (const mutation of mutations){\n                if (mutation.type === \"childList\") {\n                    emblaApi.reInit();\n                    eventHandler.emit(\"slidesChanged\");\n                    break;\n                }\n            }\n        }\n        mutationObserver = new MutationObserver((mutations)=>{\n            if (destroyed) return;\n            if (isBoolean(watchSlides) || watchSlides(emblaApi, mutations)) {\n                defaultCallback(mutations);\n            }\n        });\n        mutationObserver.observe(container, {\n            childList: true\n        });\n    }\n    function destroy() {\n        if (mutationObserver) mutationObserver.disconnect();\n        destroyed = true;\n    }\n    const self = {\n        init,\n        destroy\n    };\n    return self;\n}\nfunction SlidesInView(container, slides, eventHandler, threshold) {\n    const intersectionEntryMap = {};\n    let inViewCache = null;\n    let notInViewCache = null;\n    let intersectionObserver;\n    let destroyed = false;\n    function init() {\n        intersectionObserver = new IntersectionObserver((entries)=>{\n            if (destroyed) return;\n            entries.forEach((entry)=>{\n                const index = slides.indexOf(entry.target);\n                intersectionEntryMap[index] = entry;\n            });\n            inViewCache = null;\n            notInViewCache = null;\n            eventHandler.emit(\"slidesInView\");\n        }, {\n            root: container.parentElement,\n            threshold\n        });\n        slides.forEach((slide)=>intersectionObserver.observe(slide));\n    }\n    function destroy() {\n        if (intersectionObserver) intersectionObserver.disconnect();\n        destroyed = true;\n    }\n    function createInViewList(inView) {\n        return objectKeys(intersectionEntryMap).reduce((list, slideIndex)=>{\n            const index = parseInt(slideIndex);\n            const { isIntersecting } = intersectionEntryMap[index];\n            const inViewMatch = inView && isIntersecting;\n            const notInViewMatch = !inView && !isIntersecting;\n            if (inViewMatch || notInViewMatch) list.push(index);\n            return list;\n        }, []);\n    }\n    function get(inView = true) {\n        if (inView && inViewCache) return inViewCache;\n        if (!inView && notInViewCache) return notInViewCache;\n        const slideIndexes = createInViewList(inView);\n        if (inView) inViewCache = slideIndexes;\n        if (!inView) notInViewCache = slideIndexes;\n        return slideIndexes;\n    }\n    const self = {\n        init,\n        destroy,\n        get\n    };\n    return self;\n}\nfunction SlideSizes(axis, containerRect, slideRects, slides, readEdgeGap, ownerWindow) {\n    const { measureSize, startEdge, endEdge } = axis;\n    const withEdgeGap = slideRects[0] && readEdgeGap;\n    const startGap = measureStartGap();\n    const endGap = measureEndGap();\n    const slideSizes = slideRects.map(measureSize);\n    const slideSizesWithGaps = measureWithGaps();\n    function measureStartGap() {\n        if (!withEdgeGap) return 0;\n        const slideRect = slideRects[0];\n        return mathAbs(containerRect[startEdge] - slideRect[startEdge]);\n    }\n    function measureEndGap() {\n        if (!withEdgeGap) return 0;\n        const style = ownerWindow.getComputedStyle(arrayLast(slides));\n        return parseFloat(style.getPropertyValue(`margin-${endEdge}`));\n    }\n    function measureWithGaps() {\n        return slideRects.map((rect, index, rects)=>{\n            const isFirst = !index;\n            const isLast = arrayIsLastIndex(rects, index);\n            if (isFirst) return slideSizes[index] + startGap;\n            if (isLast) return slideSizes[index] + endGap;\n            return rects[index + 1][startEdge] - rect[startEdge];\n        }).map(mathAbs);\n    }\n    const self = {\n        slideSizes,\n        slideSizesWithGaps,\n        startGap,\n        endGap\n    };\n    return self;\n}\nfunction SlidesToScroll(axis, viewSize, slidesToScroll, loop, containerRect, slideRects, startGap, endGap, pixelTolerance) {\n    const { startEdge, endEdge, direction } = axis;\n    const groupByNumber = isNumber(slidesToScroll);\n    function byNumber(array, groupSize) {\n        return arrayKeys(array).filter((i)=>i % groupSize === 0).map((i)=>array.slice(i, i + groupSize));\n    }\n    function bySize(array) {\n        if (!array.length) return [];\n        return arrayKeys(array).reduce((groups, rectB, index)=>{\n            const rectA = arrayLast(groups) || 0;\n            const isFirst = rectA === 0;\n            const isLast = rectB === arrayLastIndex(array);\n            const edgeA = containerRect[startEdge] - slideRects[rectA][startEdge];\n            const edgeB = containerRect[startEdge] - slideRects[rectB][endEdge];\n            const gapA = !loop && isFirst ? direction(startGap) : 0;\n            const gapB = !loop && isLast ? direction(endGap) : 0;\n            const chunkSize = mathAbs(edgeB - gapB - (edgeA + gapA));\n            if (index && chunkSize > viewSize + pixelTolerance) groups.push(rectB);\n            if (isLast) groups.push(array.length);\n            return groups;\n        }, []).map((currentSize, index, groups)=>{\n            const previousSize = Math.max(groups[index - 1] || 0);\n            return array.slice(previousSize, currentSize);\n        });\n    }\n    function groupSlides(array) {\n        return groupByNumber ? byNumber(array, slidesToScroll) : bySize(array);\n    }\n    const self = {\n        groupSlides\n    };\n    return self;\n}\nfunction Engine(root, container, slides, ownerDocument, ownerWindow, options, eventHandler) {\n    // Options\n    const { align, axis: scrollAxis, direction, startIndex, loop, duration, dragFree, dragThreshold, inViewThreshold, slidesToScroll: groupSlides, skipSnaps, containScroll, watchResize, watchSlides, watchDrag } = options;\n    // Measurements\n    const pixelTolerance = 2;\n    const nodeRects = NodeRects();\n    const containerRect = nodeRects.measure(container);\n    const slideRects = slides.map(nodeRects.measure);\n    const axis = Axis(scrollAxis, direction);\n    const viewSize = axis.measureSize(containerRect);\n    const percentOfView = PercentOfView(viewSize);\n    const alignment = Alignment(align, viewSize);\n    const containSnaps = !loop && !!containScroll;\n    const readEdgeGap = loop || !!containScroll;\n    const { slideSizes, slideSizesWithGaps, startGap, endGap } = SlideSizes(axis, containerRect, slideRects, slides, readEdgeGap, ownerWindow);\n    const slidesToScroll = SlidesToScroll(axis, viewSize, groupSlides, loop, containerRect, slideRects, startGap, endGap, pixelTolerance);\n    const { snaps, snapsAligned } = ScrollSnaps(axis, alignment, containerRect, slideRects, slidesToScroll);\n    const contentSize = -arrayLast(snaps) + arrayLast(slideSizesWithGaps);\n    const { snapsContained, scrollContainLimit } = ScrollContain(viewSize, contentSize, snapsAligned, containScroll, pixelTolerance);\n    const scrollSnaps = containSnaps ? snapsContained : snapsAligned;\n    const { limit } = ScrollLimit(contentSize, scrollSnaps, loop);\n    // Indexes\n    const index = Counter(arrayLastIndex(scrollSnaps), startIndex, loop);\n    const indexPrevious = index.clone();\n    const slideIndexes = arrayKeys(slides);\n    // Animation\n    const update = ({ dragHandler, scrollBody, scrollBounds, options: { loop } })=>{\n        if (!loop) scrollBounds.constrain(dragHandler.pointerDown());\n        scrollBody.seek();\n    };\n    const render = ({ scrollBody, translate, location, offsetLocation, scrollLooper, slideLooper, dragHandler, animation, eventHandler, options: { loop } }, lagOffset)=>{\n        const velocity = scrollBody.velocity();\n        const hasSettled = scrollBody.settled();\n        if (hasSettled && !dragHandler.pointerDown()) {\n            animation.stop();\n            eventHandler.emit(\"settle\");\n        }\n        if (!hasSettled) eventHandler.emit(\"scroll\");\n        offsetLocation.set(location.get() - velocity + velocity * lagOffset);\n        if (loop) {\n            scrollLooper.loop(scrollBody.direction());\n            slideLooper.loop();\n        }\n        translate.to(offsetLocation.get());\n    };\n    const animation = Animations(ownerDocument, ownerWindow, ()=>update(engine), (lagOffset)=>render(engine, lagOffset));\n    // Shared\n    const friction = 0.68;\n    const startLocation = scrollSnaps[index.get()];\n    const location = Vector1D(startLocation);\n    const offsetLocation = Vector1D(startLocation);\n    const target = Vector1D(startLocation);\n    const scrollBody = ScrollBody(location, offsetLocation, target, duration, friction);\n    const scrollTarget = ScrollTarget(loop, scrollSnaps, contentSize, limit, target);\n    const scrollTo = ScrollTo(animation, index, indexPrevious, scrollBody, scrollTarget, target, eventHandler);\n    const scrollProgress = ScrollProgress(limit);\n    const eventStore = EventStore();\n    const slidesInView = SlidesInView(container, slides, eventHandler, inViewThreshold);\n    const { slideRegistry } = SlideRegistry(containSnaps, containScroll, scrollSnaps, scrollContainLimit, slidesToScroll, slideIndexes);\n    const slideFocus = SlideFocus(root, slides, slideRegistry, scrollTo, scrollBody, eventStore, eventHandler);\n    // Engine\n    const engine = {\n        ownerDocument,\n        ownerWindow,\n        eventHandler,\n        containerRect,\n        slideRects,\n        animation,\n        axis,\n        dragHandler: DragHandler(axis, root, ownerDocument, ownerWindow, target, DragTracker(axis, ownerWindow), location, animation, scrollTo, scrollBody, scrollTarget, index, eventHandler, percentOfView, dragFree, dragThreshold, skipSnaps, friction, watchDrag),\n        eventStore,\n        percentOfView,\n        index,\n        indexPrevious,\n        limit,\n        location,\n        offsetLocation,\n        options,\n        resizeHandler: ResizeHandler(container, eventHandler, ownerWindow, slides, axis, watchResize, nodeRects),\n        scrollBody,\n        scrollBounds: ScrollBounds(limit, offsetLocation, target, scrollBody, percentOfView),\n        scrollLooper: ScrollLooper(contentSize, limit, offsetLocation, [\n            location,\n            offsetLocation,\n            target\n        ]),\n        scrollProgress,\n        scrollSnapList: scrollSnaps.map(scrollProgress.get),\n        scrollSnaps,\n        scrollTarget,\n        scrollTo,\n        slideLooper: SlideLooper(axis, viewSize, contentSize, slideSizes, slideSizesWithGaps, snaps, scrollSnaps, offsetLocation, slides),\n        slideFocus,\n        slidesHandler: SlidesHandler(container, eventHandler, watchSlides),\n        slidesInView,\n        slideIndexes,\n        slideRegistry,\n        slidesToScroll,\n        target,\n        translate: Translate(axis, container)\n    };\n    return engine;\n}\nfunction EventHandler() {\n    let listeners = {};\n    let api;\n    function init(emblaApi) {\n        api = emblaApi;\n    }\n    function getListeners(evt) {\n        return listeners[evt] || [];\n    }\n    function emit(evt) {\n        getListeners(evt).forEach((e)=>e(api, evt));\n        return self;\n    }\n    function on(evt, cb) {\n        listeners[evt] = getListeners(evt).concat([\n            cb\n        ]);\n        return self;\n    }\n    function off(evt, cb) {\n        listeners[evt] = getListeners(evt).filter((e)=>e !== cb);\n        return self;\n    }\n    function clear() {\n        listeners = {};\n    }\n    const self = {\n        init,\n        emit,\n        off,\n        on,\n        clear\n    };\n    return self;\n}\nconst defaultOptions = {\n    align: \"center\",\n    axis: \"x\",\n    container: null,\n    slides: null,\n    containScroll: \"trimSnaps\",\n    direction: \"ltr\",\n    slidesToScroll: 1,\n    inViewThreshold: 0,\n    breakpoints: {},\n    dragFree: false,\n    dragThreshold: 10,\n    loop: false,\n    skipSnaps: false,\n    duration: 25,\n    startIndex: 0,\n    active: true,\n    watchDrag: true,\n    watchResize: true,\n    watchSlides: true\n};\nfunction OptionsHandler(ownerWindow) {\n    function mergeOptions(optionsA, optionsB) {\n        return objectsMergeDeep(optionsA, optionsB || {});\n    }\n    function optionsAtMedia(options) {\n        const optionsAtMedia = options.breakpoints || {};\n        const matchedMediaOptions = objectKeys(optionsAtMedia).filter((media)=>ownerWindow.matchMedia(media).matches).map((media)=>optionsAtMedia[media]).reduce((a, mediaOption)=>mergeOptions(a, mediaOption), {});\n        return mergeOptions(options, matchedMediaOptions);\n    }\n    function optionsMediaQueries(optionsList) {\n        return optionsList.map((options)=>objectKeys(options.breakpoints || {})).reduce((acc, mediaQueries)=>acc.concat(mediaQueries), []).map(ownerWindow.matchMedia);\n    }\n    const self = {\n        mergeOptions,\n        optionsAtMedia,\n        optionsMediaQueries\n    };\n    return self;\n}\nfunction PluginsHandler(optionsHandler) {\n    let activePlugins = [];\n    function init(emblaApi, plugins) {\n        activePlugins = plugins.filter(({ options })=>optionsHandler.optionsAtMedia(options).active !== false);\n        activePlugins.forEach((plugin)=>plugin.init(emblaApi, optionsHandler));\n        return plugins.reduce((map, plugin)=>Object.assign(map, {\n                [plugin.name]: plugin\n            }), {});\n    }\n    function destroy() {\n        activePlugins = activePlugins.filter((plugin)=>plugin.destroy());\n    }\n    const self = {\n        init,\n        destroy\n    };\n    return self;\n}\nfunction EmblaCarousel(root, userOptions, userPlugins) {\n    const ownerDocument = root.ownerDocument;\n    const ownerWindow = ownerDocument.defaultView;\n    const optionsHandler = OptionsHandler(ownerWindow);\n    const pluginsHandler = PluginsHandler(optionsHandler);\n    const mediaHandlers = EventStore();\n    const eventHandler = EventHandler();\n    const { mergeOptions, optionsAtMedia, optionsMediaQueries } = optionsHandler;\n    const { on, off, emit } = eventHandler;\n    const reInit = reActivate;\n    let destroyed = false;\n    let engine;\n    let optionsBase = mergeOptions(defaultOptions, EmblaCarousel.globalOptions);\n    let options = mergeOptions(optionsBase);\n    let pluginList = [];\n    let pluginApis;\n    let container;\n    let slides;\n    function storeElements() {\n        const { container: userContainer, slides: userSlides } = options;\n        const customContainer = isString(userContainer) ? root.querySelector(userContainer) : userContainer;\n        container = customContainer || root.children[0];\n        const customSlides = isString(userSlides) ? container.querySelectorAll(userSlides) : userSlides;\n        slides = [].slice.call(customSlides || container.children);\n    }\n    function createEngine(options) {\n        const engine = Engine(root, container, slides, ownerDocument, ownerWindow, options, eventHandler);\n        if (options.loop && !engine.slideLooper.canLoop()) {\n            const optionsWithoutLoop = Object.assign({}, options, {\n                loop: false\n            });\n            return createEngine(optionsWithoutLoop);\n        }\n        return engine;\n    }\n    function activate(withOptions, withPlugins) {\n        if (destroyed) return;\n        optionsBase = mergeOptions(optionsBase, withOptions);\n        options = optionsAtMedia(optionsBase);\n        pluginList = withPlugins || pluginList;\n        storeElements();\n        engine = createEngine(options);\n        optionsMediaQueries([\n            optionsBase,\n            ...pluginList.map(({ options })=>options)\n        ]).forEach((query)=>mediaHandlers.add(query, \"change\", reActivate));\n        if (!options.active) return;\n        engine.translate.to(engine.location.get());\n        engine.animation.init();\n        engine.slidesInView.init();\n        engine.slideFocus.init();\n        engine.eventHandler.init(self);\n        engine.resizeHandler.init(self);\n        engine.slidesHandler.init(self);\n        if (engine.options.loop) engine.slideLooper.loop();\n        if (container.offsetParent && slides.length) engine.dragHandler.init(self);\n        pluginApis = pluginsHandler.init(self, pluginList);\n    }\n    function reActivate(withOptions, withPlugins) {\n        const startIndex = selectedScrollSnap();\n        deActivate();\n        activate(mergeOptions({\n            startIndex\n        }, withOptions), withPlugins);\n        eventHandler.emit(\"reInit\");\n    }\n    function deActivate() {\n        engine.dragHandler.destroy();\n        engine.eventStore.clear();\n        engine.translate.clear();\n        engine.slideLooper.clear();\n        engine.resizeHandler.destroy();\n        engine.slidesHandler.destroy();\n        engine.slidesInView.destroy();\n        engine.animation.destroy();\n        pluginsHandler.destroy();\n        mediaHandlers.clear();\n    }\n    function destroy() {\n        if (destroyed) return;\n        destroyed = true;\n        mediaHandlers.clear();\n        deActivate();\n        eventHandler.emit(\"destroy\");\n        eventHandler.clear();\n    }\n    function scrollTo(index, jump, direction) {\n        if (!options.active || destroyed) return;\n        engine.scrollBody.useBaseFriction().useDuration(jump === true ? 0 : options.duration);\n        engine.scrollTo.index(index, direction || 0);\n    }\n    function scrollNext(jump) {\n        const next = engine.index.add(1).get();\n        scrollTo(next, jump, -1);\n    }\n    function scrollPrev(jump) {\n        const prev = engine.index.add(-1).get();\n        scrollTo(prev, jump, 1);\n    }\n    function canScrollNext() {\n        const next = engine.index.add(1).get();\n        return next !== selectedScrollSnap();\n    }\n    function canScrollPrev() {\n        const prev = engine.index.add(-1).get();\n        return prev !== selectedScrollSnap();\n    }\n    function scrollSnapList() {\n        return engine.scrollSnapList;\n    }\n    function scrollProgress() {\n        return engine.scrollProgress.get(engine.location.get());\n    }\n    function selectedScrollSnap() {\n        return engine.index.get();\n    }\n    function previousScrollSnap() {\n        return engine.indexPrevious.get();\n    }\n    function slidesInView() {\n        return engine.slidesInView.get();\n    }\n    function slidesNotInView() {\n        return engine.slidesInView.get(false);\n    }\n    function plugins() {\n        return pluginApis;\n    }\n    function internalEngine() {\n        return engine;\n    }\n    function rootNode() {\n        return root;\n    }\n    function containerNode() {\n        return container;\n    }\n    function slideNodes() {\n        return slides;\n    }\n    const self = {\n        canScrollNext,\n        canScrollPrev,\n        containerNode,\n        internalEngine,\n        destroy,\n        off,\n        on,\n        emit,\n        plugins,\n        previousScrollSnap,\n        reInit,\n        rootNode,\n        scrollNext,\n        scrollPrev,\n        scrollProgress,\n        scrollSnapList,\n        scrollTo,\n        selectedScrollSnap,\n        slideNodes,\n        slidesInView,\n        slidesNotInView\n    };\n    activate(userOptions, userPlugins);\n    setTimeout(()=>eventHandler.emit(\"init\"), 0);\n    return self;\n}\nEmblaCarousel.globalOptions = undefined;\n //# sourceMappingURL=embla-carousel.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/embla-carousel/esm/embla-carousel.esm.js\n");

/***/ })

};
;