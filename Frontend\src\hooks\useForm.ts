'use client';
import getUserIPData from 'common/getUserIpData';
import getUserTrackingData from 'common/getUserTrackingData';
import { useState } from 'react';

export default function useForm(
  initialValues,
  initialErrors,
  variant = 'default',
  source,
  pdf_link = '',
) {
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState(initialErrors);
  const [errorMessages, setErrorMessages] = useState({
    empty: '',
    invalid: '',
  });

  // Define required fields dynamically based on variant
  const requiredFields =
    variant === 'caseStudy'
      ? ['firstName', 'emailAddress', 'phoneNumber']
      : ['firstName', 'lastName', 'emailAddress', 'phoneNumber', 'consent'];

  const constructErrorMessages = newErrors => {
    const newErrorMessages = { ...errorMessages };

    // Check only required fields based on variant
    if (requiredFields.some(field => newErrors[field]?.empty)) {
      newErrorMessages.empty = 'Please fill the highlighted fields';
    } else {
      newErrorMessages.empty = '';
    }

    if (newErrors.emailAddress?.invalid && newErrors.phoneNumber?.invalid) {
      newErrorMessages.invalid = 'Please enter valid Email ID and Phone Number';
    } else if (newErrors.emailAddress?.invalid) {
      newErrorMessages.invalid = 'Please enter a valid Email ID';
    } else if (newErrors.phoneNumber?.invalid) {
      newErrorMessages.invalid = 'Please enter a valid Phone Number';
    } else {
      newErrorMessages.invalid = '';
    }

    setErrorMessages(newErrorMessages);
  };

  const validateField = (name, value) => {
    const newErrors: { [key: string]: { empty: boolean; invalid: boolean } } = {
      ...errors,
    };

    if (!value) {
      newErrors[name] = { empty: true, invalid: false };
    } else if (name === 'emailAddress' && !/\S+@\S+\.\S+/.test(value)) {
      newErrors[name] = { empty: false, invalid: true };
    } else if (name === 'phoneNumber' && !/.{6,}/.test(value)) {
      newErrors[name] = { empty: false, invalid: true };
    } else {
      newErrors[name] = initialErrors[name];
    }

    setErrors(newErrors);
    constructErrorMessages(newErrors);
  };

  const handleBlur = ({ name, value }) => {
    const newValues = { ...values };
    if (typeof value === 'string') {
      newValues[name] = value.trim();
    }
    setValues(newValues);
    if (name in errors) {
      validateField(name, value);
    }
  };

  const handleChange = ({ name, value, type = '', checked = false }) => {
    value = type === 'checkbox' ? checked : value;
    const newValues = { ...values };

    if (name === 'firstName' || name === 'lastName') {
      newValues[name] = value.replace(/[^a-zA-Z0-9 ]/g, '').trimStart();
    } else if (name === 'emailAddress') {
      newValues[name] = value.replace(' ', '');
    } else {
      newValues[name] = value;
    }

    setValues(newValues);
    if (name in errors) {
      validateField(name, value);
    }
  };

  const validateForm = () => {
    const newErrors = { ...errors };

    // Ensure each field has an empty object if it doesn't exis
    requiredFields.forEach(field => {
      if (!values[field]) {
        newErrors[field] = { empty: true, invalid: false }; // Explicitly set both properties
      }
    });

    setErrors(newErrors);
    constructErrorMessages(newErrors);

    return !Object.values(newErrors).some(
      (error: { empty: boolean; invalid: boolean }) =>
        error.empty || error.invalid,
    );
  };

  const handleSubmit = async e => {
    e.preventDefault();
    if (validateForm()) {
      try {
        const userIPData = await getUserIPData();
        const userTrackingData = await getUserTrackingData();

        const formData = {
          firstName: values.firstName || '',
          lastName: values.lastName || '',
          emailAddress: values.emailAddress || '',
          phoneNumber: values.phoneNumber || '',
          howDidYouHearAboutUs: values.howDidYouHearAboutUs || '',
          companyName: values.companyName || '',
          howCanWeHelpYou: values.howCanWeHelpYou || '',
          utm_campaign: userTrackingData.utm_campaign || '',
          utm_medium: userTrackingData.utm_medium || '',
          utm_source: userTrackingData.utm_source || '',
          ip_address: userIPData.ipAddress || '',
          ga_4_userid: userTrackingData.ga_client_id || '',
          city: userIPData.location.city || '',
          country: userIPData.location.country || '',
          secondary_source: source || '',
          clarity: userTrackingData.clarity || '',
          url: window.location.href || '',
          referrer: userTrackingData.referrer || '',
          consent: values.consent || false,
        };

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_FORM_SUBMISSION_URL}/contact-us`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': process.env.NEXT_PUBLIC_X_API_KEY || '',
            },
            body: JSON.stringify(formData),
          },
        );

        if (response.ok) {
          if (
            (source === 'CaseStudy' ||
              source === 'eBooks' ||
              source === 'whitePapers') &&
            pdf_link
          ) {
            window.open(pdf_link, '_blank');
          }
          window.location.href = '/thank-you/';
        } else {
          console.error('Error submitting form:', await response.json());
        }
      } catch (error) {
        console.error('Error in form submission:', error);
      }

      setValues(initialValues);
      setErrors(initialErrors);
    }
  };

  const handleSubmitAIReadiness = async (
    data,
    newResult,
    handleVisibleSection,
  ) => {
    if (validateForm()) {
      try {
        const userIPData = await getUserIPData();
        const userTrackingData = await getUserTrackingData();

        const formData = {
          firstName: values.firstName || '',
          lastName: values.lastName || '',
          emailAddress: values.emailAddress || '',
          phoneNumber: values.phoneNumber || '',
          companyName: values.companyName || '',
          utm_campaign: userTrackingData.utm_campaign || '',
          utm_medium: userTrackingData.utm_medium || '',
          utm_source: userTrackingData.utm_source || '',
          ip_address: userIPData.ipAddress || '',
          ga_4_userid: userTrackingData.ga_client_id || '',
          city: userIPData.location.city || '',
          country: userIPData.location.country || '',
          secondary_source: source || '',
          clarity: userTrackingData.clarity || '',
          url: window.location.href || '',
          referrer: userTrackingData.referrer || '',
          consent: values.consent || false,
          do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_:
            data[0][0][0] || '',
          how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_:
            data[0][1][0] || '',
          do_you_have_budget_allocated_for_your_ai_project_:
            data[0][2][0] || '',
          do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_:
            data[1][0][0] || '',
          which_of_the_below_db_tools_do_you_currently_use_:
            data[1][1][0] || '',
          is_the_relevant_data_for_the_ai_project_available_and_accessible_:
            data[1][2][0] || '',
          do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__:
            data[1][3][0] || '',
          how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib:
            data[1][4][0] || '',
          does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_:
            data[2][0][0] || '',
          do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_:
            data[3][0][0] || '',
          do_you_have_risk_management_strategies_in_place_for_the_ai_project_:
            data[3][1][0] || '',
          do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions:
            data[4][0][0] || '',
          strategy___leadership: newResult[0] || '',
          data_readiness___infrastructure: newResult[1] || '',
          talent___skills: newResult[2] || '',
          execution___monitoring: newResult[3] || '',
          impact_evaliation: newResult[4] || '',
          average_of_all_score: newResult['final'] || '',
        };

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_FORM_SUBMISSION_URL}/ai-readiness`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': process.env.NEXT_PUBLIC_X_API_KEY || '',
            },
            body: JSON.stringify(formData),
          },
        );

        if (response.ok) {
          // if (
          //   (source === 'CaseStudy' ||
          //     source === 'eBooks' ||
          //     source === 'whitePapers') &&
          //   pdf_link
          // ) {
          //   window.open(pdf_link, '_blank');
          // }
          // window.location.href = '/thank-you/';
          handleVisibleSection(data.length);
        } else {
          console.error('Error submitting form:', await response.json());
        }
      } catch (error) {
        console.error('Error in form submission:', error);
      }

      setValues(initialValues);
      setErrors(initialErrors);
    }
  };

  const handleSubmitCloudMigration = async (
    data,
    calculationResult,
    handleVisibleSection,
  ) => {
    if (validateForm()) {
      try {
        const userIPData = await getUserIPData();
        const userTrackingData = await getUserTrackingData();

        // Map the form data to match the expected payload structure
        const formData = {
          // Basic form fields
          firstName: values.firstName || '',
          lastName: values.lastName || '',
          emailAddress: values.emailAddress || '',
          companyName: values.companyName || '',
          phoneNumber: values.phoneNumber || '',
          howCanWeHelpYou: values.howCanWeHelpYou || '',
          consent: values.consent || '',

          // Location and tracking data
          city: userIPData?.location?.city || '',
          country: userIPData?.location?.country || '',
          ip_address: userIPData?.ipAddress || '',
          ga_4_userid: userTrackingData?.ga_client_id || '',
          clarity: userTrackingData?.clarity || '',
          utm_campaign: userTrackingData?.utm_campaign || '',
          utm_medium: userTrackingData?.utm_medium || '',
          utm_source: userTrackingData?.utm_source || '',
          referrer: userTrackingData?.referrer || '',
          url: window.location.href,
          secondary_source: 'Cloud Migration Cost Calculator',

          // Calculation results
          lowerRange: calculationResult?.lowerRange || 0,
          upperRange: calculationResult?.upperRange || 0,
          totalCost: calculationResult?.totalCost || 0,
          costFactors: calculationResult?.costFactors || {},
          calculationData: data || {},

          // Map question answers to the expected field names based on the payload structure
          which_elements_are_you_planning_to_migrate_to_the_cloud: data[0]?.[0]?.[0] || '',
          approximately_how_many_servers_do_you_intend_to_migrate: data[0]?.[1]?.[0] || '',
          what_is_the_type_of_data_migration_you_intend_to_do: data[0]?.[2]?.[0] || '',
          what_is_your_current_it_infrastructure_setup: data[0]?.[3]?.[0] || '',
          what_is_the_total_capacity_of_your_servers: data[0]?.[4]?.[0] || '',
          what_is_the_current_monthly_infrastructure_cost_of_your_current_setup: data[0]?.[5]?.[0] || '',
          what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud: data[0]?.[6]?.[0]?.split(',') || [],
          what_type_of_workloads_do_you_run: (() => {
            const rawData = data[1]?.[0]?.[0];
            if (!rawData) return [];

            const answers = rawData.split(',');
            const processedAnswers: string[] = [];

            answers.forEach((answer: string) => {
              if (answer.startsWith('OTHER:')) {
                // Extract custom values from "OTHER:value1,value2,value3" format
                const customValues = answer.substring(6); // Remove "OTHER:" prefix
                if (customValues.trim()) {
                  const customValuesList = customValues.split(',').map((val: string) => val.trim()).filter((val: string) => val);
                  processedAnswers.push(...customValuesList);
                }
              } else {
                // Skip generic "Other (please specify)" options, keep regular answers
                const isGenericOther = answer.toLowerCase().includes('other') && answer.toLowerCase().includes('specify');
                if (!isGenericOther) {
                  processedAnswers.push(answer);
                }
              }
            });

            return processedAnswers;
          })(),
          what_is_the_average_cpu_and_memory_usage_of_your_workloads: data[1]?.[1]?.[0] || '',
          do_you_require_high_availability_or_disaster_recovery_for_critical_applications: data[1]?.[2]?.[0] || '',
          which_cloud_provider_s_are_you_considering: data[2]?.[0]?.[0] || '',
          do_you_plan_to_use_reserved_instances_spot_instances_or_pay_as_you_go_pricing_models: data[2]?.[1]?.[0] || '',
          which_cloud_environments_are_you_planning_to_deploy: data[2]?.[2]?.[0]?.split(',') || [],
          do_you_have_any_specific_compliance_or_regulatory_requirements_that_your_cloud_migration_needs_to_meet: data[3]?.[0]?.[0] || '',
          what_migration_strategy_do_you_prefer: data[3]?.[1]?.[0]?.split(',') || [],
          do_you_need_auto_scaling_capabilities_for_cost_optimization: data[4][0][0] || '',
          how_often_do_you_plan_to_review_and_optimize_your_cloud_expenses: data[4]?.[1]?.[0] || '',
          minimum_cost: calculationResult?.lowerRange || 0,
          maximum_migration_cost: calculationResult?.upperRange || 0,
        };

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_FORM_SUBMISSION_URL}/cloud-migration-cost-calculator`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': process.env.NEXT_PUBLIC_X_API_KEY || '',
            },
            body: JSON.stringify(formData),
          },
        );

        if (response.ok) {
          handleVisibleSection(data.length); // Move to results section
        } else {
          console.error('Error submitting form:', await response.json());
        }
      } catch (error) {
        console.log("error:: ", error)
        console.error('Error in form submission:', error);
      }

      setValues(initialValues);
      setErrors(initialErrors);
    }
  };

  return {
    values,
    errors,
    errorMessages,
    handleChange,
    handleBlur,
    handleSubmit,
    handleSubmitAIReadiness,
    handleSubmitCloudMigration,
  };
}
