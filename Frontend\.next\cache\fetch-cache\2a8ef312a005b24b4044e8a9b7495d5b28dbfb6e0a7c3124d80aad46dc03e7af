{"kind": "FETCH", "data": {"headers": {"access-control-allow-credentials": "true", "access-control-allow-origin": "", "connection": "keep-alive", "content-length": "772", "content-security-policy": "script-src 'self' 'unsafe-inline' cdn.marutitech.com;img-src 'self' data: strapi.io cdn.marutitech.com storage.googleapis.com cdn-gcp.new.marutitech.com;media-src cdn.marutitech.com cdn-gcp.new.marutitech.com blob:;connect-src 'self' https:;default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';style-src 'self' https: 'unsafe-inline'", "content-type": "application/json; charset=utf-8", "date": "Tue, 05 Aug 2025 07:18:31 GMT", "referrer-policy": "no-referrer", "server": "nginx/1.24.0", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Origin", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-powered-by": "Strapi <strapi.io>"}, "body": "eyJkYXRhIjp7ImlkIjoxLCJhdHRyaWJ1dGVzIjp7ImNyZWF0ZWRBdCI6IjIwMjUtMDMtMTFUMDk6MjA6NTMuNTM4WiIsInVwZGF0ZWRBdCI6IjIwMjUtMDMtMTJUMDY6MjY6MzguMjY2WiIsInB1Ymxpc2hlZEF0IjoiMjAyNS0wMy0xMVQwOToyMDo1NC44MjFaIiwiZm9ybSI6eyJpZCI6MjcsInRpdGxlIjoiQ29udGFjdCB1cyIsImluc3RydWN0aW9ucyI6IigqKSBBc3RlcmlzayBkZW5vdGVzIG1hbmRhdG9yeSBmaWVsZHMiLCJjb25zZW50X3N0YXRlbWVudCI6IkkgY29uc2VudCB0byBwcm9jZXNzaW5nIG9mIG15IHBlcnNvbmFsIGRhdGEgZW50ZXJlZCBhYm92ZSBmb3IgTWFydXRpIFRlY2hsYWJzIHRvIGNvbnRhY3QgbWUuIiwiTGlua2VkSW5CdXR0b25fdGl0bGUiOm51bGwsImJ1dHRvbiI6eyJpZCI6MTQwLCJ0aXRsZSI6IkxldOKAmXMgVGFsayIsImxpbmsiOm51bGx9LCJmb3JtRmllbGRzIjp7ImlkIjoyOCwiZmllbGROYW1lRm9yX0ZpcnN0TmFtZSI6IkZpcnN0IE5hbWUiLCJmaWVsZE5hbWVGb3JfTGFzdE5hbWUiOiJMYXN0IE5hbWUiLCJmaWVsZE5hbWVGb3JfRW1haWxBZGRyZXNzIjoiRW1haWwgQWRkcmVzcyIsImZpZWxkTmFtZUZvcl9Db21wYW55TmFtZSI6IkNvbXBhbnkgTmFtZSIsImZpZWxkTmFtZUZvcl9QaG9uZU51bWJlciI6IlBob25lIE51bWJlciIsImZpZWxkTmFtZUZvcl9Ib3dDYW5XZUhlbHBZb3UiOiJIb3cgY2FuIHdlIGhlbHAgeW91PyIsImZpZWxkTmFtZUZvcl9Ib3dEaWRZb3VIZWFyQWJvdXRVcyI6bnVsbH19fX0sIm1ldGEiOnt9fQ==", "status": 200, "url": "https://dev-content.marutitech.com/api/form?populate=form.formFields&populate=form.button"}, "revalidate": 31536000, "tags": []}