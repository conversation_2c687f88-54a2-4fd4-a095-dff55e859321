"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/embla-carousel-auto-scroll";
exports.ids = ["vendor-chunks/embla-carousel-auto-scroll"];
exports.modules = {

/***/ "(ssr)/../../node_modules/embla-carousel-auto-scroll/esm/embla-carousel-auto-scroll.esm.js":
/*!*******************************************************************************************!*\
  !*** ../../node_modules/embla-carousel-auto-scroll/esm/embla-carousel-auto-scroll.esm.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AutoScroll)\n/* harmony export */ });\nconst defaultOptions = {\n    direction: \"forward\",\n    speed: 2,\n    startDelay: 1000,\n    active: true,\n    breakpoints: {},\n    playOnInit: true,\n    stopOnFocusIn: true,\n    stopOnInteraction: true,\n    stopOnMouseEnter: false,\n    rootNode: null\n};\nfunction AutoScroll(userOptions = {}) {\n    let options;\n    let emblaApi;\n    let destroyed;\n    let playing = false;\n    let resume = true;\n    let timer = 0;\n    let startDelay;\n    let defaultScrollBehaviour;\n    function init(emblaApiInstance, optionsHandler) {\n        emblaApi = emblaApiInstance;\n        const { mergeOptions, optionsAtMedia } = optionsHandler;\n        const optionsBase = mergeOptions(defaultOptions, AutoScroll.globalOptions);\n        const allOptions = mergeOptions(optionsBase, userOptions);\n        options = optionsAtMedia(allOptions);\n        if (emblaApi.scrollSnapList().length <= 1) return;\n        startDelay = options.startDelay;\n        destroyed = false;\n        defaultScrollBehaviour = emblaApi.internalEngine().scrollBody;\n        const { eventStore } = emblaApi.internalEngine();\n        const emblaRoot = emblaApi.rootNode();\n        const root = options.rootNode && options.rootNode(emblaRoot) || emblaRoot;\n        const container = emblaApi.containerNode();\n        emblaApi.on(\"pointerDown\", stopScroll);\n        if (!options.stopOnInteraction) {\n            emblaApi.on(\"pointerUp\", startScrollOnSettle);\n        }\n        if (options.stopOnMouseEnter) {\n            eventStore.add(root, \"mouseenter\", ()=>{\n                resume = false;\n                stopScroll();\n            });\n            if (!options.stopOnInteraction) {\n                eventStore.add(root, \"mouseleave\", ()=>{\n                    resume = true;\n                    startScroll();\n                });\n            }\n        }\n        if (options.stopOnFocusIn) {\n            eventStore.add(container, \"focusin\", ()=>{\n                stopScroll();\n                emblaApi.scrollTo(emblaApi.selectedScrollSnap(), true);\n            });\n            if (!options.stopOnInteraction) {\n                eventStore.add(container, \"focusout\", startScroll);\n            }\n        }\n        if (options.playOnInit) startScroll();\n    }\n    function destroy() {\n        emblaApi.off(\"pointerDown\", stopScroll).off(\"pointerUp\", startScrollOnSettle).off(\"settle\", onSettle);\n        stopScroll();\n        destroyed = true;\n        playing = false;\n    }\n    function startScroll() {\n        if (destroyed || playing) return;\n        if (!resume) return;\n        emblaApi.emit(\"autoScroll:play\");\n        const engine = emblaApi.internalEngine();\n        const { ownerWindow } = engine;\n        timer = ownerWindow.setTimeout(()=>{\n            engine.scrollBody = createAutoScrollBehaviour(engine);\n            engine.animation.start();\n        }, startDelay);\n        playing = true;\n    }\n    function stopScroll() {\n        if (destroyed || !playing) return;\n        emblaApi.emit(\"autoScroll:stop\");\n        const engine = emblaApi.internalEngine();\n        const { ownerWindow } = engine;\n        engine.scrollBody = defaultScrollBehaviour;\n        ownerWindow.clearTimeout(timer);\n        timer = 0;\n        playing = false;\n    }\n    function onSettle() {\n        if (resume) startScroll();\n        emblaApi.off(\"settle\", onSettle);\n    }\n    function startScrollOnSettle() {\n        emblaApi.on(\"settle\", onSettle);\n    }\n    function createAutoScrollBehaviour(engine) {\n        const { location, target, scrollTarget, index, indexPrevious, limit: { reachedMin, reachedMax, constrain }, options: { loop } } = engine;\n        const directionSign = options.direction === \"forward\" ? -1 : 1;\n        const noop = ()=>self;\n        let bodyVelocity = 0;\n        let scrollDirection = 0;\n        let rawLocation = location.get();\n        let rawLocationPrevious = 0;\n        let hasSettled = false;\n        function seek() {\n            let directionDiff = 0;\n            bodyVelocity = directionSign * options.speed;\n            rawLocation += bodyVelocity;\n            location.add(bodyVelocity);\n            target.set(location);\n            directionDiff = rawLocation - rawLocationPrevious;\n            scrollDirection = Math.sign(directionDiff);\n            rawLocationPrevious = rawLocation;\n            const currentIndex = scrollTarget.byDistance(0, false).index;\n            if (index.get() !== currentIndex) {\n                indexPrevious.set(index.get());\n                index.set(currentIndex);\n                emblaApi.emit(\"select\");\n            }\n            const reachedEnd = options.direction === \"forward\" ? reachedMin(location.get()) : reachedMax(location.get());\n            if (!loop && reachedEnd) {\n                hasSettled = true;\n                const constrainedLocation = constrain(location.get());\n                location.set(constrainedLocation);\n                target.set(location);\n                stopScroll();\n            }\n            return self;\n        }\n        const self = {\n            direction: ()=>scrollDirection,\n            duration: ()=>-1,\n            velocity: ()=>bodyVelocity,\n            settled: ()=>hasSettled,\n            seek,\n            useBaseFriction: noop,\n            useBaseDuration: noop,\n            useFriction: noop,\n            useDuration: noop\n        };\n        return self;\n    }\n    function play(startDelayOverride) {\n        if (typeof startDelayOverride !== \"undefined\") {\n            startDelay = startDelayOverride;\n        }\n        resume = true;\n        startScroll();\n    }\n    function stop() {\n        if (playing) stopScroll();\n    }\n    function reset() {\n        if (playing) {\n            stopScroll();\n            startScrollOnSettle();\n        }\n    }\n    function isPlaying() {\n        return playing;\n    }\n    const self = {\n        name: \"autoScroll\",\n        options: userOptions,\n        init,\n        destroy,\n        play,\n        stop,\n        reset,\n        isPlaying\n    };\n    return self;\n}\nAutoScroll.globalOptions = undefined;\n //# sourceMappingURL=embla-carousel-auto-scroll.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/embla-carousel-auto-scroll/esm/embla-carousel-auto-scroll.esm.js\n");

/***/ })

};
;