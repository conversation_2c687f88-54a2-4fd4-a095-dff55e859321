"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/embla-carousel-autoplay";
exports.ids = ["vendor-chunks/embla-carousel-autoplay"];
exports.modules = {

/***/ "(ssr)/../../node_modules/embla-carousel-autoplay/esm/embla-carousel-autoplay.esm.js":
/*!*************************************************************************************!*\
  !*** ../../node_modules/embla-carousel-autoplay/esm/embla-carousel-autoplay.esm.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Autoplay)\n/* harmony export */ });\nconst defaultOptions = {\n    active: true,\n    breakpoints: {},\n    delay: 4000,\n    jump: false,\n    playOnInit: true,\n    stopOnFocusIn: true,\n    stopOnInteraction: true,\n    stopOnMouseEnter: false,\n    stopOnLastSnap: false,\n    rootNode: null\n};\nfunction Autoplay(userOptions = {}) {\n    let options;\n    let emblaApi;\n    let destroyed;\n    let playing = false;\n    let resume = true;\n    let jump = false;\n    let timer = 0;\n    function init(emblaApiInstance, optionsHandler) {\n        emblaApi = emblaApiInstance;\n        const { mergeOptions, optionsAtMedia } = optionsHandler;\n        const optionsBase = mergeOptions(defaultOptions, Autoplay.globalOptions);\n        const allOptions = mergeOptions(optionsBase, userOptions);\n        options = optionsAtMedia(allOptions);\n        if (emblaApi.scrollSnapList().length <= 1) return;\n        jump = options.jump;\n        destroyed = false;\n        const { eventStore, ownerDocument } = emblaApi.internalEngine();\n        const emblaRoot = emblaApi.rootNode();\n        const root = options.rootNode && options.rootNode(emblaRoot) || emblaRoot;\n        const container = emblaApi.containerNode();\n        emblaApi.on(\"pointerDown\", stopTimer);\n        if (!options.stopOnInteraction) {\n            emblaApi.on(\"pointerUp\", startTimer);\n        }\n        if (options.stopOnMouseEnter) {\n            eventStore.add(root, \"mouseenter\", ()=>{\n                resume = false;\n                stopTimer();\n            });\n            if (!options.stopOnInteraction) {\n                eventStore.add(root, \"mouseleave\", ()=>{\n                    resume = true;\n                    startTimer();\n                });\n            }\n        }\n        if (options.stopOnFocusIn) {\n            eventStore.add(container, \"focusin\", stopTimer);\n            if (!options.stopOnInteraction) {\n                eventStore.add(container, \"focusout\", startTimer);\n            }\n        }\n        eventStore.add(ownerDocument, \"visibilitychange\", visibilityChange);\n        if (options.playOnInit && !documentIsHidden()) startTimer();\n    }\n    function destroy() {\n        emblaApi.off(\"pointerDown\", stopTimer).off(\"pointerUp\", startTimer);\n        stopTimer();\n        destroyed = true;\n        playing = false;\n    }\n    function startTimer() {\n        if (destroyed) return;\n        if (!resume) return;\n        if (!playing) emblaApi.emit(\"autoplay:play\");\n        const { ownerWindow } = emblaApi.internalEngine();\n        ownerWindow.clearInterval(timer);\n        timer = ownerWindow.setInterval(next, options.delay);\n        playing = true;\n    }\n    function stopTimer() {\n        if (destroyed) return;\n        if (playing) emblaApi.emit(\"autoplay:stop\");\n        const { ownerWindow } = emblaApi.internalEngine();\n        ownerWindow.clearInterval(timer);\n        timer = 0;\n        playing = false;\n    }\n    function visibilityChange() {\n        if (documentIsHidden()) {\n            resume = playing;\n            return stopTimer();\n        }\n        if (resume) startTimer();\n    }\n    function documentIsHidden() {\n        const { ownerDocument } = emblaApi.internalEngine();\n        return ownerDocument.visibilityState === \"hidden\";\n    }\n    function play(jumpOverride) {\n        if (typeof jumpOverride !== \"undefined\") jump = jumpOverride;\n        resume = true;\n        startTimer();\n    }\n    function stop() {\n        if (playing) stopTimer();\n    }\n    function reset() {\n        if (playing) play();\n    }\n    function isPlaying() {\n        return playing;\n    }\n    function next() {\n        const { index } = emblaApi.internalEngine();\n        const nextIndex = index.clone().add(1).get();\n        const lastIndex = emblaApi.scrollSnapList().length - 1;\n        const kill = options.stopOnLastSnap && nextIndex === lastIndex;\n        if (kill) stopTimer();\n        if (emblaApi.canScrollNext()) {\n            emblaApi.scrollNext(jump);\n        } else {\n            emblaApi.scrollTo(0, jump);\n        }\n    }\n    const self = {\n        name: \"autoplay\",\n        options: userOptions,\n        init,\n        destroy,\n        play,\n        stop,\n        reset,\n        isPlaying\n    };\n    return self;\n}\nAutoplay.globalOptions = undefined;\n //# sourceMappingURL=embla-carousel-autoplay.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/embla-carousel-autoplay/esm/embla-carousel-autoplay.esm.js\n");

/***/ })

};
;