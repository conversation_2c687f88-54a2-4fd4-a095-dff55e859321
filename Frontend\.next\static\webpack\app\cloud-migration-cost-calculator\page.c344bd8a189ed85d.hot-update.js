"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/cloud-migration-cost-calculator/page",{

/***/ "(app-pages-browser)/./src/hooks/useForm.ts":
/*!******************************!*\
  !*** ./src/hooks/useForm.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ useForm; }\n/* harmony export */ });\n/* harmony import */ var common_getUserIpData__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! common/getUserIpData */ \"(app-pages-browser)/./src/common/getUserIpData.ts\");\n/* harmony import */ var common_getUserTrackingData__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! common/getUserTrackingData */ \"(app-pages-browser)/./src/common/getUserTrackingData.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction useForm(initialValues, initialErrors) {\n    let variant = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"default\", source = arguments.length > 3 ? arguments[3] : void 0, pdf_link = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : \"\";\n    const [values, setValues] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(initialValues);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(initialErrors);\n    const [errorMessages, setErrorMessages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        empty: \"\",\n        invalid: \"\"\n    });\n    // Define required fields dynamically based on variant\n    const requiredFields = variant === \"caseStudy\" ? [\n        \"firstName\",\n        \"emailAddress\",\n        \"phoneNumber\"\n    ] : [\n        \"firstName\",\n        \"lastName\",\n        \"emailAddress\",\n        \"phoneNumber\",\n        \"consent\"\n    ];\n    const constructErrorMessages = (newErrors)=>{\n        var _newErrors_emailAddress, _newErrors_phoneNumber, _newErrors_emailAddress1, _newErrors_phoneNumber1;\n        const newErrorMessages = {\n            ...errorMessages\n        };\n        // Check only required fields based on variant\n        if (requiredFields.some((field)=>{\n            var _newErrors_field;\n            return (_newErrors_field = newErrors[field]) === null || _newErrors_field === void 0 ? void 0 : _newErrors_field.empty;\n        })) {\n            newErrorMessages.empty = \"Please fill the highlighted fields\";\n        } else {\n            newErrorMessages.empty = \"\";\n        }\n        if (((_newErrors_emailAddress = newErrors.emailAddress) === null || _newErrors_emailAddress === void 0 ? void 0 : _newErrors_emailAddress.invalid) && ((_newErrors_phoneNumber = newErrors.phoneNumber) === null || _newErrors_phoneNumber === void 0 ? void 0 : _newErrors_phoneNumber.invalid)) {\n            newErrorMessages.invalid = \"Please enter valid Email ID and Phone Number\";\n        } else if ((_newErrors_emailAddress1 = newErrors.emailAddress) === null || _newErrors_emailAddress1 === void 0 ? void 0 : _newErrors_emailAddress1.invalid) {\n            newErrorMessages.invalid = \"Please enter a valid Email ID\";\n        } else if ((_newErrors_phoneNumber1 = newErrors.phoneNumber) === null || _newErrors_phoneNumber1 === void 0 ? void 0 : _newErrors_phoneNumber1.invalid) {\n            newErrorMessages.invalid = \"Please enter a valid Phone Number\";\n        } else {\n            newErrorMessages.invalid = \"\";\n        }\n        setErrorMessages(newErrorMessages);\n    };\n    const validateField = (name, value)=>{\n        const newErrors = {\n            ...errors\n        };\n        if (!value) {\n            newErrors[name] = {\n                empty: true,\n                invalid: false\n            };\n        } else if (name === \"emailAddress\" && !/\\S+@\\S+\\.\\S+/.test(value)) {\n            newErrors[name] = {\n                empty: false,\n                invalid: true\n            };\n        } else if (name === \"phoneNumber\" && !/.{6,}/.test(value)) {\n            newErrors[name] = {\n                empty: false,\n                invalid: true\n            };\n        } else {\n            newErrors[name] = initialErrors[name];\n        }\n        setErrors(newErrors);\n        constructErrorMessages(newErrors);\n    };\n    const handleBlur = (param)=>{\n        let { name, value } = param;\n        const newValues = {\n            ...values\n        };\n        if (typeof value === \"string\") {\n            newValues[name] = value.trim();\n        }\n        setValues(newValues);\n        if (name in errors) {\n            validateField(name, value);\n        }\n    };\n    const handleChange = (param)=>{\n        let { name, value, type = \"\", checked = false } = param;\n        value = type === \"checkbox\" ? checked : value;\n        const newValues = {\n            ...values\n        };\n        if (name === \"firstName\" || name === \"lastName\") {\n            newValues[name] = value.replace(/[^a-zA-Z0-9 ]/g, \"\").trimStart();\n        } else if (name === \"emailAddress\") {\n            newValues[name] = value.replace(\" \", \"\");\n        } else {\n            newValues[name] = value;\n        }\n        setValues(newValues);\n        if (name in errors) {\n            validateField(name, value);\n        }\n    };\n    const validateForm = ()=>{\n        const newErrors = {\n            ...errors\n        };\n        // Ensure each field has an empty object if it doesn't exis\n        requiredFields.forEach((field)=>{\n            if (!values[field]) {\n                newErrors[field] = {\n                    empty: true,\n                    invalid: false\n                }; // Explicitly set both properties\n            }\n        });\n        setErrors(newErrors);\n        constructErrorMessages(newErrors);\n        return !Object.values(newErrors).some((error)=>error.empty || error.invalid);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (validateForm()) {\n            try {\n                const userIPData = await (0,common_getUserIpData__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n                const userTrackingData = await (0,common_getUserTrackingData__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n                const formData = {\n                    firstName: values.firstName || \"\",\n                    lastName: values.lastName || \"\",\n                    emailAddress: values.emailAddress || \"\",\n                    phoneNumber: values.phoneNumber || \"\",\n                    howDidYouHearAboutUs: values.howDidYouHearAboutUs || \"\",\n                    companyName: values.companyName || \"\",\n                    howCanWeHelpYou: values.howCanWeHelpYou || \"\",\n                    utm_campaign: userTrackingData.utm_campaign || \"\",\n                    utm_medium: userTrackingData.utm_medium || \"\",\n                    utm_source: userTrackingData.utm_source || \"\",\n                    ip_address: userIPData.ipAddress || \"\",\n                    ga_4_userid: userTrackingData.ga_client_id || \"\",\n                    city: userIPData.location.city || \"\",\n                    country: userIPData.location.country || \"\",\n                    secondary_source: source || \"\",\n                    clarity: userTrackingData.clarity || \"\",\n                    url: window.location.href || \"\",\n                    referrer: userTrackingData.referrer || \"\",\n                    consent: values.consent || false\n                };\n                const response = await fetch(\"\".concat(\"https://test-api.marutitech.com\", \"/contact-us\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"x-api-key\": \"989TBz66u18qbn4Brzeyt8jVHNNDEhsA4jwXTUO4\" || 0\n                    },\n                    body: JSON.stringify(formData)\n                });\n                if (response.ok) {\n                    if ((source === \"CaseStudy\" || source === \"eBooks\" || source === \"whitePapers\") && pdf_link) {\n                        window.open(pdf_link, \"_blank\");\n                    }\n                    window.location.href = \"/thank-you/\";\n                } else {\n                    console.error(\"Error submitting form:\", await response.json());\n                }\n            } catch (error) {\n                console.error(\"Error in form submission:\", error);\n            }\n            setValues(initialValues);\n            setErrors(initialErrors);\n        }\n    };\n    const handleSubmitAIReadiness = async (data, newResult, handleVisibleSection)=>{\n        if (validateForm()) {\n            try {\n                const userIPData = await (0,common_getUserIpData__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n                const userTrackingData = await (0,common_getUserTrackingData__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n                const formData = {\n                    firstName: values.firstName || \"\",\n                    lastName: values.lastName || \"\",\n                    emailAddress: values.emailAddress || \"\",\n                    phoneNumber: values.phoneNumber || \"\",\n                    companyName: values.companyName || \"\",\n                    utm_campaign: userTrackingData.utm_campaign || \"\",\n                    utm_medium: userTrackingData.utm_medium || \"\",\n                    utm_source: userTrackingData.utm_source || \"\",\n                    ip_address: userIPData.ipAddress || \"\",\n                    ga_4_userid: userTrackingData.ga_client_id || \"\",\n                    city: userIPData.location.city || \"\",\n                    country: userIPData.location.country || \"\",\n                    secondary_source: source || \"\",\n                    clarity: userTrackingData.clarity || \"\",\n                    url: window.location.href || \"\",\n                    referrer: userTrackingData.referrer || \"\",\n                    consent: values.consent || false,\n                    do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_: data[0][0][0] || \"\",\n                    how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_: data[0][1][0] || \"\",\n                    do_you_have_budget_allocated_for_your_ai_project_: data[0][2][0] || \"\",\n                    do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_: data[1][0][0] || \"\",\n                    which_of_the_below_db_tools_do_you_currently_use_: data[1][1][0] || \"\",\n                    is_the_relevant_data_for_the_ai_project_available_and_accessible_: data[1][2][0] || \"\",\n                    do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__: data[1][3][0] || \"\",\n                    how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib: data[1][4][0] || \"\",\n                    does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_: data[2][0][0] || \"\",\n                    do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_: data[3][0][0] || \"\",\n                    do_you_have_risk_management_strategies_in_place_for_the_ai_project_: data[3][1][0] || \"\",\n                    do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions: data[4][0][0] || \"\",\n                    strategy___leadership: newResult[0] || \"\",\n                    data_readiness___infrastructure: newResult[1] || \"\",\n                    talent___skills: newResult[2] || \"\",\n                    execution___monitoring: newResult[3] || \"\",\n                    impact_evaliation: newResult[4] || \"\",\n                    average_of_all_score: newResult[\"final\"] || \"\"\n                };\n                const response = await fetch(\"\".concat(\"https://test-api.marutitech.com\", \"/ai-readiness\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"x-api-key\": \"989TBz66u18qbn4Brzeyt8jVHNNDEhsA4jwXTUO4\" || 0\n                    },\n                    body: JSON.stringify(formData)\n                });\n                if (response.ok) {\n                    // if (\n                    //   (source === 'CaseStudy' ||\n                    //     source === 'eBooks' ||\n                    //     source === 'whitePapers') &&\n                    //   pdf_link\n                    // ) {\n                    //   window.open(pdf_link, '_blank');\n                    // }\n                    // window.location.href = '/thank-you/';\n                    handleVisibleSection(data.length);\n                } else {\n                    console.error(\"Error submitting form:\", await response.json());\n                }\n            } catch (error) {\n                console.error(\"Error in form submission:\", error);\n            }\n            setValues(initialValues);\n            setErrors(initialErrors);\n        }\n    };\n    const handleSubmitCloudMigration = async (data, calculationResult, handleVisibleSection)=>{\n        if (validateForm()) {\n            try {\n                var _userIPData_location, _userIPData_location1, _data__, _data_, _data__1, _data_1, _data__2, _data_2, _data__3, _data_3, _data__4, _data_4, _data__5, _data_5, _data___, _data__6, _data_6, _data__7, _data_7, _data__8, _data_8, _data__9, _data_9, _data__10, _data_10, _data___1, _data__11, _data_11, _data__12, _data_12, _data___2, _data__13, _data_13, _data__14, _data_14;\n                const userIPData = await (0,common_getUserIpData__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n                const userTrackingData = await (0,common_getUserTrackingData__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n                console.log(\"data: \", data);\n                // Map the form data to match the expected payload structure\n                const formData = {\n                    // Basic form fields\n                    firstName: values.firstName || \"\",\n                    lastName: values.lastName || \"\",\n                    emailAddress: values.emailAddress || \"\",\n                    companyName: values.companyName || \"\",\n                    phoneNumber: values.phoneNumber || \"\",\n                    howCanWeHelpYou: values.howCanWeHelpYou || \"\",\n                    consent: values.consent || \"\",\n                    // Location and tracking data\n                    city: (userIPData === null || userIPData === void 0 ? void 0 : (_userIPData_location = userIPData.location) === null || _userIPData_location === void 0 ? void 0 : _userIPData_location.city) || \"\",\n                    country: (userIPData === null || userIPData === void 0 ? void 0 : (_userIPData_location1 = userIPData.location) === null || _userIPData_location1 === void 0 ? void 0 : _userIPData_location1.country) || \"\",\n                    ip_address: (userIPData === null || userIPData === void 0 ? void 0 : userIPData.ipAddress) || \"\",\n                    ga_4_userid: (userTrackingData === null || userTrackingData === void 0 ? void 0 : userTrackingData.ga_client_id) || \"\",\n                    clarity: (userTrackingData === null || userTrackingData === void 0 ? void 0 : userTrackingData.clarity) || \"\",\n                    utm_campaign: (userTrackingData === null || userTrackingData === void 0 ? void 0 : userTrackingData.utm_campaign) || \"\",\n                    utm_medium: (userTrackingData === null || userTrackingData === void 0 ? void 0 : userTrackingData.utm_medium) || \"\",\n                    utm_source: (userTrackingData === null || userTrackingData === void 0 ? void 0 : userTrackingData.utm_source) || \"\",\n                    referrer: (userTrackingData === null || userTrackingData === void 0 ? void 0 : userTrackingData.referrer) || \"\",\n                    url: window.location.href,\n                    secondary_source: \"Cloud Migration Cost Calculator\",\n                    // Calculation results\n                    lowerRange: (calculationResult === null || calculationResult === void 0 ? void 0 : calculationResult.lowerRange) || 0,\n                    upperRange: (calculationResult === null || calculationResult === void 0 ? void 0 : calculationResult.upperRange) || 0,\n                    totalCost: (calculationResult === null || calculationResult === void 0 ? void 0 : calculationResult.totalCost) || 0,\n                    costFactors: (calculationResult === null || calculationResult === void 0 ? void 0 : calculationResult.costFactors) || {},\n                    calculationData: data || {},\n                    // Map question answers to the expected field names based on the payload structure\n                    which_elements_are_you_planning_to_migrate_to_the_cloud: ((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : (_data__ = _data_[0]) === null || _data__ === void 0 ? void 0 : _data__[0]) || \"\",\n                    approximately_how_many_servers_do_you_intend_to_migrate: ((_data_1 = data[0]) === null || _data_1 === void 0 ? void 0 : (_data__1 = _data_1[1]) === null || _data__1 === void 0 ? void 0 : _data__1[0]) || \"\",\n                    what_is_the_type_of_data_migration_you_intend_to_do: ((_data_2 = data[0]) === null || _data_2 === void 0 ? void 0 : (_data__2 = _data_2[2]) === null || _data__2 === void 0 ? void 0 : _data__2[0]) || \"\",\n                    what_is_your_current_it_infrastructure_setup: ((_data_3 = data[0]) === null || _data_3 === void 0 ? void 0 : (_data__3 = _data_3[3]) === null || _data__3 === void 0 ? void 0 : _data__3[0]) || \"\",\n                    what_is_the_total_capacity_of_your_servers: ((_data_4 = data[0]) === null || _data_4 === void 0 ? void 0 : (_data__4 = _data_4[4]) === null || _data__4 === void 0 ? void 0 : _data__4[0]) || \"\",\n                    what_is_the_current_monthly_infrastructure_cost_of_your_current_setup: ((_data_5 = data[0]) === null || _data_5 === void 0 ? void 0 : (_data__5 = _data_5[5]) === null || _data__5 === void 0 ? void 0 : _data__5[0]) || \"\",\n                    what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud: ((_data_6 = data[0]) === null || _data_6 === void 0 ? void 0 : (_data__6 = _data_6[6]) === null || _data__6 === void 0 ? void 0 : (_data___ = _data__6[0]) === null || _data___ === void 0 ? void 0 : _data___.split(\",\")) || [],\n                    what_type_of_workloads_do_you_run: (()=>{\n                        var _data__, _data_;\n                        const rawData = (_data_ = data[1]) === null || _data_ === void 0 ? void 0 : (_data__ = _data_[0]) === null || _data__ === void 0 ? void 0 : _data__[0];\n                        if (!rawData) return [];\n                        const answers = rawData.split(\",\");\n                        const processedAnswers = [];\n                        // Get \"Other\" input values from localStorage if available\n                        const otherInputValues = JSON.parse(localStorage.getItem(\"otherInputValues\") || \"{}\");\n                        const otherInputKey = \"1_0\"; // section 1, question 0 (workload types)\n                        const otherCustomValues = otherInputValues[otherInputKey];\n                        answers.forEach((answer)=>{\n                            const isOtherOption = answer.toLowerCase().includes(\"other\") && answer.toLowerCase().includes(\"specify\");\n                            if (isOtherOption && otherCustomValues && otherCustomValues.trim()) {\n                                // Replace \"Other\" with custom values\n                                const customValuesList = otherCustomValues.split(\",\").map((val)=>val.trim()).filter((val)=>val);\n                                processedAnswers.push(...customValuesList);\n                            } else if (!isOtherOption) {\n                                // Keep regular answers\n                                processedAnswers.push(answer);\n                            }\n                        // Skip \"Other\" options without custom values\n                        });\n                        return processedAnswers;\n                    })(),\n                    what_is_the_average_cpu_and_memory_usage_of_your_workloads: ((_data_7 = data[1]) === null || _data_7 === void 0 ? void 0 : (_data__7 = _data_7[2]) === null || _data__7 === void 0 ? void 0 : _data__7[0]) || \"\",\n                    do_you_require_high_availability_or_disaster_recovery_for_critical_applications: ((_data_8 = data[1]) === null || _data_8 === void 0 ? void 0 : (_data__8 = _data_8[2]) === null || _data__8 === void 0 ? void 0 : _data__8[0]) || \"\",\n                    which_cloud_provider_s_are_you_considering: ((_data_9 = data[2]) === null || _data_9 === void 0 ? void 0 : (_data__9 = _data_9[0]) === null || _data__9 === void 0 ? void 0 : _data__9[0]) || \"\",\n                    do_you_plan_to_use_reserved_instances_spot_instances_or_pay_as_you_go_pricing_models: ((_data_10 = data[2]) === null || _data_10 === void 0 ? void 0 : (_data__10 = _data_10[1]) === null || _data__10 === void 0 ? void 0 : _data__10[0]) || \"\",\n                    which_cloud_environments_are_you_planning_to_deploy: ((_data_11 = data[2]) === null || _data_11 === void 0 ? void 0 : (_data__11 = _data_11[2]) === null || _data__11 === void 0 ? void 0 : (_data___1 = _data__11[0]) === null || _data___1 === void 0 ? void 0 : _data___1.split(\",\")) || [],\n                    do_you_have_any_specific_compliance_or_regulatory_requirements_that_your_cloud_migration_needs_to_meet: ((_data_12 = data[3]) === null || _data_12 === void 0 ? void 0 : (_data__12 = _data_12[0]) === null || _data__12 === void 0 ? void 0 : _data__12[0]) || \"\",\n                    what_migration_strategy_do_you_prefer: ((_data_13 = data[3]) === null || _data_13 === void 0 ? void 0 : (_data__13 = _data_13[1]) === null || _data__13 === void 0 ? void 0 : (_data___2 = _data__13[0]) === null || _data___2 === void 0 ? void 0 : _data___2.split(\",\")) || [],\n                    do_you_need_auto_scaling_capabilities_for_cost_optimization: data[4][0][0] || \"\",\n                    how_often_do_you_plan_to_review_and_optimize_your_cloud_expenses: ((_data_14 = data[4]) === null || _data_14 === void 0 ? void 0 : (_data__14 = _data_14[1]) === null || _data__14 === void 0 ? void 0 : _data__14[0]) || \"\",\n                    minimum_cost: (calculationResult === null || calculationResult === void 0 ? void 0 : calculationResult.lowerRange) || 0,\n                    maximum_migration_cost: (calculationResult === null || calculationResult === void 0 ? void 0 : calculationResult.upperRange) || 0\n                };\n                const response = await fetch(\"\".concat(\"https://test-api.marutitech.com\", \"/cloud-migration-cost-calculator\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"x-api-key\": \"989TBz66u18qbn4Brzeyt8jVHNNDEhsA4jwXTUO4\" || 0\n                    },\n                    body: JSON.stringify(formData)\n                });\n                if (response.ok) {\n                    handleVisibleSection(data.length); // Move to results section\n                } else {\n                    console.error(\"Error submitting form:\", await response.json());\n                }\n            } catch (error) {\n                console.log(\"error:: \", error);\n                console.error(\"Error in form submission:\", error);\n            }\n            setValues(initialValues);\n            setErrors(initialErrors);\n        }\n    };\n    return {\n        values,\n        errors,\n        errorMessages,\n        handleChange,\n        handleBlur,\n        handleSubmit,\n        handleSubmitAIReadiness,\n        handleSubmitCloudMigration\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useForm.ts\n"));

/***/ })

});